import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/errors/error_handler.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/inventory_entities.dart';
import '../../domain/repositories/inventory_repository.dart';

/// Inventory repository implementation
@LazySingleton(as: InventoryRepository)
class InventoryRepositoryImpl implements InventoryRepository {
  final ApiClient _apiClient;

  const InventoryRepositoryImpl(this._apiClient);

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getInventoryItems({
    InventoryFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock inventory items for development
      final items = _getMockInventoryItems();
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 25,
          totalPages: 2,
          hasNextPage: true,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> getInventoryItemById(
    String itemId,
  ) async {
    try {
      // Return mock inventory item for development
      final item = _getMockInventoryItem(itemId);
      return Right(ApiResponse.success(data: item));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> createInventoryItem(
    CreateInventoryItemRequest request,
  ) async {
    try {
      final requestData = {
        'item_code': request.itemCode,
        'item_name': request.itemName,
        'description': request.description,
        'category': request.category.value,
        'type': request.type.name,
        'unit': request.unit,
        'initial_stock': request.initialStock,
        'minimum_stock': request.minimumStock,
        'maximum_stock': request.maximumStock,
        'reorder_point': request.reorderPoint,
        'reorder_quantity': request.reorderQuantity,
        'unit_cost': request.unitCost,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.inventoryItems,
        data: requestData,
      );

      if (response.statusCode == 201 && response.data != null) {
        final item = _parseInventoryItem(response.data!);
        return Right(ApiResponse.success(data: item));
      } else {
        return Left(ServerFailure(
          'Failed to create inventory item',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> updateInventoryItem(
    UpdateInventoryItemRequest request,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      
      if (request.itemName != null) requestData['item_name'] = request.itemName;
      if (request.description != null) requestData['description'] = request.description;
      if (request.minimumStock != null) requestData['minimum_stock'] = request.minimumStock;
      if (request.maximumStock != null) requestData['maximum_stock'] = request.maximumStock;
      if (request.reorderPoint != null) requestData['reorder_point'] = request.reorderPoint;
      if (request.reorderQuantity != null) requestData['reorder_quantity'] = request.reorderQuantity;
      if (request.unitCost != null) requestData['unit_cost'] = request.unitCost;

      final response = await _apiClient.put<Map<String, dynamic>>(
        '${ApiConstants.inventoryItems}/${request.itemId}',
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final item = _parseInventoryItem(response.data!);
        return Right(ApiResponse.success(data: item));
      } else {
        return Left(ServerFailure(
          'Failed to update inventory item',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteInventoryItem(
    String itemId,
    String? reason,
  ) async {
    try {
      final requestData = <String, dynamic>{};
      if (reason != null) requestData['reason'] = reason;

      final response = await _apiClient.delete<Map<String, dynamic>>(
        '${ApiConstants.inventoryItems}/$itemId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        return const Right(ApiVoidResponse(success: true));
      } else {
        return Left(ServerFailure(
          'Failed to delete inventory item',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> updateStockLevel(
    String itemId,
    double newStock,
    String reason,
  ) async {
    try {
      final requestData = {
        'new_stock': newStock,
        'reason': reason,
      };

      final response = await _apiClient.patch<Map<String, dynamic>>(
        '${ApiConstants.inventoryItems}/$itemId/stock',
        data: requestData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final item = _parseInventoryItem(response.data!);
        return Right(ApiResponse.success(data: item));
      } else {
        return Left(ServerFailure(
          'Failed to update stock level',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<StockMovement>>> adjustStock(
    AdjustStockRequest request,
  ) async {
    try {
      final requestData = {
        'item_id': request.itemId,
        'quantity': request.quantity,
        'movement_type': request.movementType.value,
        'reason': request.reason,
        'reference_number': request.referenceNumber,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        ApiConstants.stockMovements,
        data: requestData,
      );

      if (response.statusCode == 201 && response.data != null) {
        final movement = _parseStockMovement(response.data!);
        return Right(ApiResponse.success(data: movement));
      } else {
        return Left(ServerFailure(
          'Failed to adjust stock',
          code: response.statusCode?.toString(),
        ));
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getLowStockItems({
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock low stock items for development
      final items = _getMockInventoryItems()
          .where((item) => item.isLowStock)
          .toList();
      
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 5,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getOutOfStockItems({
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock out of stock items for development
      final items = _getMockInventoryItems()
          .where((item) => item.isOutOfStock)
          .toList();
      
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getOverstockedItems({
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock overstocked items for development
      final items = _getMockInventoryItems()
          .where((item) => item.isOverstocked)
          .toList();
      
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 3,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getExpiringItems({
    int daysAhead = 30,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock expiring items for development
      final items = _getMockInventoryItems()
          .where((item) => item.isExpiringSoon)
          .toList();
      
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 4,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<InventoryItem>>> searchInventoryItems({
    required String query,
    InventoryFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      // Return filtered mock items for development
      final items = _getMockInventoryItems()
          .where((item) => 
              item.itemCode.toLowerCase().contains(query.toLowerCase()) ||
              item.itemName.toLowerCase().contains(query.toLowerCase()))
          .toList();
      
      return Right(ApiListResponse<InventoryItem>(
        success: true,
        data: items,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 8,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, InventoryStatistics>> getInventoryStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
  }) async {
    try {
      // Return mock statistics for development
      return Right(_getMockInventoryStatistics());
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, InventoryValuation>> getInventoryValuation({
    DateTime? asOfDate,
    String? categoryId,
  }) async {
    try {
      // Return mock valuation for development
      return Right(_getMockInventoryValuation());
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<ReorderSuggestion>>> generateReorderSuggestions({
    String? categoryId,
    int daysAhead = 30,
  }) async {
    try {
      // Return mock reorder suggestions for development
      return Right(_getMockReorderSuggestions());
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<StockMovement>>> getStockMovements({
    StockMovementFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      // Return mock stock movements for development
      final movements = _getMockStockMovements();
      return Right(ApiListResponse<StockMovement>(
        success: true,
        data: movements,
        pagination: const Pagination(
          currentPage: 1,
          perPage: 20,
          total: 15,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      ));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Mock data methods for development
  List<InventoryItem> _getMockInventoryItems() {
    final now = DateTime.now();
    return [
      InventoryItem(
        id: '1',
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        itemCode: 'FAB-COT-001',
        itemName: 'Cotton Fabric - Navy Blue',
        description: 'Premium cotton fabric for shirt manufacturing',
        category: ItemCategory.fabric,
        type: ItemType.cotton,
        unit: 'meters',
        currentStock: 150.0,
        availableStock: 120.0,
        reservedStock: 30.0,
        minimumStock: 50.0,
        maximumStock: 500.0,
        reorderPoint: 75.0,
        reorderQuantity: 200.0,
        unitCost: 12.50,
        totalValue: 1875.0,
        supplierId: 'sup1',
        supplierName: 'Textile Mills Ltd.',
        location: 'Warehouse A',
        binLocation: 'A-01-15',
        status: ItemStatus.active,
        tags: ['cotton', 'navy', 'premium'],
        lastStockUpdate: now.subtract(const Duration(hours: 2)),
        lastOrderDate: now.subtract(const Duration(days: 15)),
      ),
      InventoryItem(
        id: '2',
        createdAt: now.subtract(const Duration(days: 25)),
        updatedAt: now.subtract(const Duration(hours: 6)),
        itemCode: 'BTN-PLT-001',
        itemName: 'Plastic Buttons - White',
        description: '15mm white plastic buttons for shirts',
        category: ItemCategory.accessory,
        type: ItemType.button,
        unit: 'pieces',
        currentStock: 2500.0,
        availableStock: 2300.0,
        reservedStock: 200.0,
        minimumStock: 1000.0,
        maximumStock: 10000.0,
        reorderPoint: 1500.0,
        reorderQuantity: 5000.0,
        unitCost: 0.05,
        totalValue: 125.0,
        supplierId: 'sup2',
        supplierName: 'Button Works Inc.',
        location: 'Warehouse B',
        binLocation: 'B-03-08',
        status: ItemStatus.active,
        tags: ['button', 'white', 'plastic'],
        lastStockUpdate: now.subtract(const Duration(hours: 6)),
        lastOrderDate: now.subtract(const Duration(days: 20)),
      ),
      InventoryItem(
        id: '3',
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 1)),
        itemCode: 'THR-POL-001',
        itemName: 'Polyester Thread - Black',
        description: 'High-strength polyester thread for sewing',
        category: ItemCategory.rawMaterial,
        type: ItemType.thread,
        unit: 'spools',
        currentStock: 25.0,
        availableStock: 20.0,
        reservedStock: 5.0,
        minimumStock: 50.0,
        maximumStock: 200.0,
        reorderPoint: 40.0,
        reorderQuantity: 100.0,
        unitCost: 3.75,
        totalValue: 93.75,
        supplierId: 'sup3',
        supplierName: 'Thread Masters Co.',
        location: 'Warehouse A',
        binLocation: 'A-02-12',
        status: ItemStatus.active,
        tags: ['thread', 'black', 'polyester'],
        lastStockUpdate: now.subtract(const Duration(days: 1)),
        lastOrderDate: now.subtract(const Duration(days: 10)),
      ),
      InventoryItem(
        id: '4',
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(hours: 12)),
        itemCode: 'ZIP-MET-001',
        itemName: 'Metal Zipper - 20cm',
        description: '20cm metal zipper for jackets',
        category: ItemCategory.accessory,
        type: ItemType.zipper,
        unit: 'pieces',
        currentStock: 0.0,
        availableStock: 0.0,
        reservedStock: 0.0,
        minimumStock: 100.0,
        maximumStock: 1000.0,
        reorderPoint: 150.0,
        reorderQuantity: 500.0,
        unitCost: 2.25,
        totalValue: 0.0,
        supplierId: 'sup4',
        supplierName: 'Zipper Solutions Ltd.',
        location: 'Warehouse B',
        binLocation: 'B-01-05',
        status: ItemStatus.active,
        tags: ['zipper', 'metal', '20cm'],
        lastStockUpdate: now.subtract(const Duration(hours: 12)),
        lastOrderDate: now.subtract(const Duration(days: 5)),
      ),
      InventoryItem(
        id: '5',
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(hours: 4)),
        itemCode: 'LAB-WOV-001',
        itemName: 'Woven Labels - Brand Logo',
        description: 'Custom woven labels with brand logo',
        category: ItemCategory.accessory,
        type: ItemType.label,
        unit: 'pieces',
        currentStock: 750.0,
        availableStock: 650.0,
        reservedStock: 100.0,
        minimumStock: 200.0,
        maximumStock: 2000.0,
        reorderPoint: 300.0,
        reorderQuantity: 1000.0,
        unitCost: 0.15,
        totalValue: 112.50,
        supplierId: 'sup5',
        supplierName: 'Label Craft Co.',
        location: 'Warehouse A',
        binLocation: 'A-03-20',
        status: ItemStatus.active,
        tags: ['label', 'woven', 'logo'],
        lastStockUpdate: now.subtract(const Duration(hours: 4)),
        lastOrderDate: now.subtract(const Duration(days: 8)),
        expiryDate: now.add(const Duration(days: 15)), // Expiring soon
      ),
    ];
  }

  List<StockMovement> _getMockStockMovements() {
    final now = DateTime.now();
    return [
      StockMovement(
        id: '1',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        itemId: '1',
        itemCode: 'FAB-COT-001',
        itemName: 'Cotton Fabric - Navy Blue',
        movementType: MovementType.purchase,
        quantity: 100.0,
        unitCost: 12.50,
        totalCost: 1250.0,
        reason: 'Stock replenishment',
        referenceNumber: 'PO-2024-001',
        supplierId: 'sup1',
        userId: 'user1',
        userName: 'John Doe',
        movementDate: now.subtract(const Duration(hours: 2)),
        stockBefore: 50.0,
        stockAfter: 150.0,
      ),
      StockMovement(
        id: '2',
        createdAt: now.subtract(const Duration(hours: 6)),
        updatedAt: now.subtract(const Duration(hours: 6)),
        itemId: '2',
        itemCode: 'BTN-PLT-001',
        itemName: 'Plastic Buttons - White',
        movementType: MovementType.production,
        quantity: -500.0,
        unitCost: 0.05,
        totalCost: -25.0,
        reason: 'Used in production order PO-2024-001',
        orderId: 'order1',
        userId: 'user2',
        userName: 'Jane Smith',
        movementDate: now.subtract(const Duration(hours: 6)),
        stockBefore: 3000.0,
        stockAfter: 2500.0,
      ),
      StockMovement(
        id: '3',
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        itemId: '3',
        itemCode: 'THR-POL-001',
        itemName: 'Polyester Thread - Black',
        movementType: MovementType.adjustment,
        quantity: -5.0,
        unitCost: 3.75,
        totalCost: -18.75,
        reason: 'Damaged during handling',
        userId: 'user3',
        userName: 'Mike Johnson',
        movementDate: now.subtract(const Duration(days: 1)),
        stockBefore: 30.0,
        stockAfter: 25.0,
      ),
    ];
  }

  InventoryItem _getMockInventoryItem(String itemId) {
    return _getMockInventoryItems().firstWhere(
      (item) => item.id == itemId,
      orElse: () => _getMockInventoryItems().first,
    );
  }

  InventoryStatistics _getMockInventoryStatistics() {
    return const InventoryStatistics(
      totalItems: 156,
      activeItems: 145,
      lowStockItems: 12,
      outOfStockItems: 5,
      expiringItems: 8,
      totalValue: 285000.0,
      averageValue: 1826.92,
      itemsByCategory: {
        'fabric': 45,
        'accessory': 68,
        'raw_material': 32,
        'packaging': 11,
      },
      valueByCategory: {
        'fabric': 125000.0,
        'accessory': 85000.0,
        'raw_material': 55000.0,
        'packaging': 20000.0,
      },
      movementsByType: {
        'purchase': 125,
        'production': 89,
        'adjustment': 23,
        'return': 8,
      },
    );
  }

  InventoryValuation _getMockInventoryValuation() {
    return InventoryValuation(
      asOfDate: DateTime.now(),
      totalValue: 285000.0,
      valueByCategory: const {
        'fabric': 125000.0,
        'accessory': 85000.0,
        'raw_material': 55000.0,
        'packaging': 20000.0,
      },
      valueByLocation: const {
        'warehouse_a': 165000.0,
        'warehouse_b': 120000.0,
      },
      items: const [
        InventoryValuationItem(
          itemId: '1',
          itemCode: 'FAB-COT-001',
          itemName: 'Cotton Fabric - Navy Blue',
          quantity: 150.0,
          unitCost: 12.50,
          totalValue: 1875.0,
        ),
      ],
    );
  }

  List<ReorderSuggestion> _getMockReorderSuggestions() {
    return const [
      ReorderSuggestion(
        itemId: '3',
        itemCode: 'THR-POL-001',
        itemName: 'Polyester Thread - Black',
        currentStock: 25.0,
        reorderPoint: 40.0,
        suggestedQuantity: 100.0,
        reason: 'Below reorder point',
        urgencyLevel: 3,
        estimatedCost: 375.0,
      ),
      ReorderSuggestion(
        itemId: '4',
        itemCode: 'ZIP-MET-001',
        itemName: 'Metal Zipper - 20cm',
        currentStock: 0.0,
        reorderPoint: 150.0,
        suggestedQuantity: 500.0,
        reason: 'Out of stock',
        urgencyLevel: 5,
        estimatedCost: 1125.0,
      ),
    ];
  }

  InventoryItem _parseInventoryItem(Map<String, dynamic> json) {
    // Parse inventory item from JSON - simplified for mock implementation
    return _getMockInventoryItems().first;
  }

  StockMovement _parseStockMovement(Map<String, dynamic> json) {
    // Parse stock movement from JSON - simplified for mock implementation
    return _getMockStockMovements().first;
  }

  // Unimplemented methods - return not implemented errors
  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> reserveStock(
    String itemId,
    double quantity,
    String reason,
    String? orderId,
  ) async {
    return const Left(UnimplementedFailure('Reserve stock not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryItem>>> releaseReservedStock(
    String itemId,
    double quantity,
    String reason,
  ) async {
    return const Left(UnimplementedFailure('Release reserved stock not implemented'));
  }

  @override
  Future<Either<Failure, ApiListResponse<StockMovement>>> getStockMovementsForItem(
    String itemId, {
    PaginationParams? pagination,
  }) async {
    return const Left(UnimplementedFailure('Get stock movements for item not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> createAutomaticReorder(
    List<String> itemIds,
  ) async {
    return const Left(UnimplementedFailure('Create automatic reorder not implemented'));
  }

  @override
  Future<Either<Failure, String>> exportInventoryData({
    InventoryFilterCriteria? filter,
    String format = 'csv',
  }) async {
    return const Left(UnimplementedFailure('Export inventory data not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<InventoryImportResult>>> importInventoryData(
    String filePath, {
    bool validateOnly = false,
  }) async {
    return const Left(UnimplementedFailure('Import inventory data not implemented'));
  }

  @override
  Future<Either<Failure, ApiResponse<BulkUpdateResult>>> bulkUpdateInventory(
    List<BulkUpdateInventoryRequest> requests,
  ) async {
    return const Left(UnimplementedFailure('Bulk update inventory not implemented'));
  }
}
