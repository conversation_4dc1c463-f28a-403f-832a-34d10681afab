import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/denim_inventory_entities.dart';

part 'denim_inventory_models.g.dart';

/// Denim Type model for Firebase
@JsonSerializable()
class DenimTypeModel extends DenimType {
  const DenimTypeModel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required String denimCode,
    required String type,
    required String description,
    required double gsm,
    required double width,
    String? supplierId,
    String? supplierName,
    bool isActive = true,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          denimCode: denimCode,
          type: type,
          description: description,
          gsm: gsm,
          width: width,
          supplierId: supplierId,
          supplierName: supplierName,
          isActive: isActive,
        );

  factory DenimTypeModel.fromJson(Map<String, dynamic> json) =>
      _$DenimTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimTypeModelToJson(this);

  factory DenimTypeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimTypeModel(
      id: doc.id,
      denimCode: data['denimCode'] ?? '',
      type: data['type'] ?? '',
      description: data['description'] ?? '',
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      width: (data['width'] ?? 0.0).toDouble(),
      supplierId: data['supplierId'],
      supplierName: data['supplierName'],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'denimCode': denimCode,
      'type': type,
      'description': description,
      'gsm': gsm,
      'width': width,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimTypeModel.fromEntity(DenimType entity) {
    return DenimTypeModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      denimCode: entity.denimCode,
      type: entity.type,
      description: entity.description,
      gsm: entity.gsm,
      width: entity.width,
      supplierId: entity.supplierId,
      supplierName: entity.supplierName,
      isActive: entity.isActive,
    );
  }
}

/// Denim Roll model for Firebase
@JsonSerializable()
class DenimRollModel extends DenimRoll {
  const DenimRollModel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required String rollId,
    required String denimTypeId,
    required String denimCode,
    required String denimType,
    required String color,
    required double width,
    required double totalPurchasedQty,
    double returnedQty = 0.0,
    double usedQty = 0.0,
    required double balanceQty,
    String? location,
    required DenimRollStatus status,
    String unit = 'yards',
    required double gsm,
    String? supplierId,
    String? supplierName,
    Map<String, dynamic> metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          rollId: rollId,
          denimTypeId: denimTypeId,
          denimCode: denimCode,
          denimType: denimType,
          color: color,
          width: width,
          totalPurchasedQty: totalPurchasedQty,
          returnedQty: returnedQty,
          usedQty: usedQty,
          balanceQty: balanceQty,
          location: location,
          status: status,
          unit: unit,
          gsm: gsm,
          supplierId: supplierId,
          supplierName: supplierName,
          metadata: metadata,
        );

  factory DenimRollModel.fromJson(Map<String, dynamic> json) =>
      _$DenimRollModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimRollModelToJson(this);

  factory DenimRollModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimRollModel(
      id: doc.id,
      rollId: data['rollId'] ?? '',
      denimTypeId: data['denimTypeId'] ?? '',
      denimCode: data['denimCode'] ?? '',
      denimType: data['denimType'] ?? '',
      color: data['color'] ?? '',
      width: (data['width'] ?? 0.0).toDouble(),
      totalPurchasedQty: (data['totalPurchasedQty'] ?? 0.0).toDouble(),
      returnedQty: (data['returnedQty'] ?? 0.0).toDouble(),
      usedQty: (data['usedQty'] ?? 0.0).toDouble(),
      balanceQty: (data['balanceQty'] ?? 0.0).toDouble(),
      location: data['location'],
      status: DenimRollStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => DenimRollStatus.inStock,
      ),
      unit: data['unit'] ?? 'yards',
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      supplierId: data['supplierId'],
      supplierName: data['supplierName'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimCode': denimCode,
      'denimType': denimType,
      'color': color,
      'width': width,
      'totalPurchasedQty': totalPurchasedQty,
      'returnedQty': returnedQty,
      'usedQty': usedQty,
      'balanceQty': balanceQty,
      'location': location,
      'status': status.value,
      'unit': unit,
      'gsm': gsm,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimRollModel.fromEntity(DenimRoll entity) {
    return DenimRollModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      rollId: entity.rollId,
      denimTypeId: entity.denimTypeId,
      denimCode: entity.denimCode,
      denimType: entity.denimType,
      color: entity.color,
      width: entity.width,
      totalPurchasedQty: entity.totalPurchasedQty,
      returnedQty: entity.returnedQty,
      usedQty: entity.usedQty,
      balanceQty: entity.balanceQty,
      location: entity.location,
      status: entity.status,
      unit: entity.unit,
      gsm: entity.gsm,
      supplierId: entity.supplierId,
      supplierName: entity.supplierName,
      metadata: entity.metadata,
    );
  }

  /// Update stock quantities
  DenimRollModel updateStock({
    double? returnedQty,
    double? usedQty,
    DenimRollStatus? status,
    String? location,
  }) {
    final newReturnedQty = returnedQty ?? this.returnedQty;
    final newUsedQty = usedQty ?? this.usedQty;
    final newBalanceQty = totalPurchasedQty - newReturnedQty - newUsedQty;

    return DenimRollModel(
      id: id,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      deletedAt: deletedAt,
      createdBy: createdBy,
      updatedBy: updatedBy,
      rollId: rollId,
      denimTypeId: denimTypeId,
      denimCode: denimCode,
      denimType: denimType,
      color: color,
      width: width,
      totalPurchasedQty: totalPurchasedQty,
      returnedQty: newReturnedQty,
      usedQty: newUsedQty,
      balanceQty: newBalanceQty,
      location: location ?? this.location,
      status: status ?? this.status,
      unit: unit,
      gsm: gsm,
      supplierId: supplierId,
      supplierName: supplierName,
      metadata: metadata,
    );
  }
}

/// Denim Purchase Record model for Firebase
@JsonSerializable()
class DenimPurchaseRecordModel extends DenimPurchaseRecord {
  const DenimPurchaseRecordModel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required int srNo,
    required DateTime purchaseDate,
    required String supplierName,
    String? supplierId,
    required String rollId,
    required String denimTypeId,
    required String denimType,
    required String color,
    required double width,
    required double length,
    String unit = 'yards',
    required double gsm,
    required double ratePerUnit,
    required double totalCost,
    required String invoiceNo,
    String? remarks,
    PurchaseStatus status = PurchaseStatus.completed,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          srNo: srNo,
          purchaseDate: purchaseDate,
          supplierName: supplierName,
          supplierId: supplierId,
          rollId: rollId,
          denimTypeId: denimTypeId,
          denimType: denimType,
          color: color,
          width: width,
          length: length,
          unit: unit,
          gsm: gsm,
          ratePerUnit: ratePerUnit,
          totalCost: totalCost,
          invoiceNo: invoiceNo,
          remarks: remarks,
          status: status,
        );

  factory DenimPurchaseRecordModel.fromJson(Map<String, dynamic> json) =>
      _$DenimPurchaseRecordModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimPurchaseRecordModelToJson(this);

  factory DenimPurchaseRecordModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimPurchaseRecordModel(
      id: doc.id,
      srNo: (data['srNo'] ?? 0).toInt(),
      purchaseDate: (data['purchaseDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      supplierName: data['supplierName'] ?? '',
      supplierId: data['supplierId'],
      rollId: data['rollId'] ?? '',
      denimTypeId: data['denimTypeId'] ?? '',
      denimType: data['denimType'] ?? '',
      color: data['color'] ?? '',
      width: (data['width'] ?? 0.0).toDouble(),
      length: (data['length'] ?? 0.0).toDouble(),
      unit: data['unit'] ?? 'yards',
      gsm: (data['gsm'] ?? 0.0).toDouble(),
      ratePerUnit: (data['ratePerUnit'] ?? 0.0).toDouble(),
      totalCost: (data['totalCost'] ?? 0.0).toDouble(),
      invoiceNo: data['invoiceNo'] ?? '',
      remarks: data['remarks'],
      status: PurchaseStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => PurchaseStatus.completed,
      ),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'srNo': srNo,
      'purchaseDate': Timestamp.fromDate(purchaseDate),
      'supplierName': supplierName,
      'supplierId': supplierId,
      'rollId': rollId,
      'denimTypeId': denimTypeId,
      'denimType': denimType,
      'color': color,
      'width': width,
      'length': length,
      'unit': unit,
      'gsm': gsm,
      'ratePerUnit': ratePerUnit,
      'totalCost': totalCost,
      'invoiceNo': invoiceNo,
      'remarks': remarks,
      'status': status.value,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimPurchaseRecordModel.fromEntity(DenimPurchaseRecord entity) {
    return DenimPurchaseRecordModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      srNo: entity.srNo,
      purchaseDate: entity.purchaseDate,
      supplierName: entity.supplierName,
      supplierId: entity.supplierId,
      rollId: entity.rollId,
      denimTypeId: entity.denimTypeId,
      denimType: entity.denimType,
      color: entity.color,
      width: entity.width,
      length: entity.length,
      unit: entity.unit,
      gsm: entity.gsm,
      ratePerUnit: entity.ratePerUnit,
      totalCost: entity.totalCost,
      invoiceNo: entity.invoiceNo,
      remarks: entity.remarks,
      status: entity.status,
    );
  }
}

/// Denim Return Record model for Firebase
@JsonSerializable()
class DenimReturnRecordModel extends DenimReturnRecord {
  const DenimReturnRecordModel({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required int srNo,
    required DateTime returnDate,
    required String rollId,
    required String supplierName,
    String? supplierId,
    required String reasonForReturn,
    required double quantityReturned,
    String unit = 'yards',
    required String debitNoteNo,
    String? remarks,
    ReturnStatus status = ReturnStatus.pending,
    double? refundAmount,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
          srNo: srNo,
          returnDate: returnDate,
          rollId: rollId,
          supplierName: supplierName,
          supplierId: supplierId,
          reasonForReturn: reasonForReturn,
          quantityReturned: quantityReturned,
          unit: unit,
          debitNoteNo: debitNoteNo,
          remarks: remarks,
          status: status,
          refundAmount: refundAmount,
        );

  factory DenimReturnRecordModel.fromJson(Map<String, dynamic> json) =>
      _$DenimReturnRecordModelFromJson(json);

  Map<String, dynamic> toJson() => _$DenimReturnRecordModelToJson(this);

  factory DenimReturnRecordModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DenimReturnRecordModel(
      id: doc.id,
      srNo: (data['srNo'] ?? 0).toInt(),
      returnDate: (data['returnDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      rollId: data['rollId'] ?? '',
      supplierName: data['supplierName'] ?? '',
      supplierId: data['supplierId'],
      reasonForReturn: data['reasonForReturn'] ?? '',
      quantityReturned: (data['quantityReturned'] ?? 0.0).toDouble(),
      unit: data['unit'] ?? 'yards',
      debitNoteNo: data['debitNoteNo'] ?? '',
      remarks: data['remarks'],
      status: ReturnStatus.values.firstWhere(
        (e) => e.value == data['status'],
        orElse: () => ReturnStatus.pending,
      ),
      refundAmount: (data['refundAmount'] as num?)?.toDouble(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'],
      updatedBy: data['updatedBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'srNo': srNo,
      'returnDate': Timestamp.fromDate(returnDate),
      'rollId': rollId,
      'supplierName': supplierName,
      'supplierId': supplierId,
      'reasonForReturn': reasonForReturn,
      'quantityReturned': quantityReturned,
      'unit': unit,
      'debitNoteNo': debitNoteNo,
      'remarks': remarks,
      'status': status.value,
      'refundAmount': refundAmount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  factory DenimReturnRecordModel.fromEntity(DenimReturnRecord entity) {
    return DenimReturnRecordModel(
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
      srNo: entity.srNo,
      returnDate: entity.returnDate,
      rollId: entity.rollId,
      supplierName: entity.supplierName,
      supplierId: entity.supplierId,
      reasonForReturn: entity.reasonForReturn,
      quantityReturned: entity.quantityReturned,
      unit: entity.unit,
      debitNoteNo: entity.debitNoteNo,
      remarks: entity.remarks,
      status: entity.status,
      refundAmount: entity.refundAmount,
    );
  }
}
