import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_dropdown.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../domain/requests/denim_inventory_requests.dart';

class DenimPurchasePage extends StatefulWidget {
  const DenimPurchasePage({super.key});

  @override
  State<DenimPurchasePage> createState() => _DenimPurchasePageState();
}

class _DenimPurchasePageState extends State<DenimPurchasePage> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Form controllers
  final _purchaseDateController = TextEditingController();
  final _supplierNameController = TextEditingController();
  final _rollIdController = TextEditingController();
  final _colorController = TextEditingController();
  final _widthController = TextEditingController();
  final _lengthController = TextEditingController();
  final _gsmController = TextEditingController();
  final _ratePerUnitController = TextEditingController();
  final _invoiceNoController = TextEditingController();
  final _remarksController = TextEditingController();

  // Form state
  DateTime _selectedDate = DateTime.now();
  DenimType? _selectedDenimType;
  String _selectedUnit = 'yards';
  double _totalCost = 0.0;
  bool _isLoading = false;

  // Mock data - in real app, this would come from repository
  final List<DenimType> _denimTypes = [
    DenimType(
      id: '1',
      denimCode: 'DN001',
      type: '12oz Indigo',
      description: 'Premium 12oz indigo denim',
      gsm: 320,
      width: 58,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    DenimType(
      id: '2',
      denimCode: 'DN002',
      type: 'Stretch Denim',
      description: 'Stretch denim with elastane',
      gsm: 280,
      width: 60,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _purchaseDateController.text = DateFormat('dd/MM/yyyy').format(_selectedDate);
    _lengthController.addListener(_calculateTotalCost);
    _ratePerUnitController.addListener(_calculateTotalCost);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _purchaseDateController.dispose();
    _supplierNameController.dispose();
    _rollIdController.dispose();
    _colorController.dispose();
    _widthController.dispose();
    _lengthController.dispose();
    _gsmController.dispose();
    _ratePerUnitController.dispose();
    _invoiceNoController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  void _calculateTotalCost() {
    final length = double.tryParse(_lengthController.text) ?? 0.0;
    final rate = double.tryParse(_ratePerUnitController.text) ?? 0.0;
    setState(() {
      _totalCost = length * rate;
    });
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
        _purchaseDateController.text = DateFormat('dd/MM/yyyy').format(date);
      });
    }
  }

  void _onDenimTypeChanged(DenimType? denimType) {
    setState(() {
      _selectedDenimType = denimType;
      if (denimType != null) {
        _widthController.text = denimType.width.toString();
        _gsmController.text = denimType.gsm.toString();
      }
    });
  }

  Future<void> _generateRollId() async {
    if (_selectedDenimType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a denim type first')),
      );
      return;
    }

    // In real app, this would call the repository to generate roll ID
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final rollId = '${_selectedDenimType!.denimCode}-$dateStr-001';
    
    setState(() {
      _rollIdController.text = rollId;
    });
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDenimType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a denim type')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CreatePurchaseRequest(
        purchaseDate: _selectedDate,
        supplierName: _supplierNameController.text.trim(),
        rollId: _rollIdController.text.trim(),
        denimTypeId: _selectedDenimType!.id,
        color: _colorController.text.trim(),
        width: double.parse(_widthController.text),
        length: double.parse(_lengthController.text),
        unit: _selectedUnit,
        gsm: double.parse(_gsmController.text),
        ratePerUnit: double.parse(_ratePerUnitController.text),
        invoiceNo: _invoiceNoController.text.trim(),
        remarks: _remarksController.text.trim().isEmpty 
            ? null 
            : _remarksController.text.trim(),
      );

      // In real app, this would call the repository to create purchase record
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Purchase record created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating purchase record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Add Denim Purchase',
        backgroundColor: AppColors.primary,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Purchase Information'),
              const SizedBox(height: 16),
              _buildPurchaseInfoSection(),
              const SizedBox(height: 24),
              
              _buildSectionHeader('Denim Details'),
              const SizedBox(height: 16),
              _buildDenimDetailsSection(),
              const SizedBox(height: 24),
              
              _buildSectionHeader('Measurements & Pricing'),
              const SizedBox(height: 16),
              _buildMeasurementsSection(),
              const SizedBox(height: 24),
              
              _buildSectionHeader('Additional Information'),
              const SizedBox(height: 16),
              _buildAdditionalInfoSection(),
              const SizedBox(height: 32),
              
              _buildSubmitButton(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildPurchaseInfoSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _purchaseDateController,
                label: 'Purchase Date',
                readOnly: true,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.calendar_today),
                  onPressed: _selectDate,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select purchase date';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _supplierNameController,
          label: 'Supplier/Mill Name',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter supplier name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _invoiceNoController,
          label: 'Invoice Number',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter invoice number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDenimDetailsSection() {
    return Column(
      children: [
        CustomDropdown<DenimType>(
          label: 'Denim Type',
          value: _selectedDenimType,
          items: _denimTypes.map((type) => DropdownMenuItem(
            value: type,
            child: Text('${type.denimCode} - ${type.type}'),
          )).toList(),
          onChanged: _onDenimTypeChanged,
          validator: (value) {
            if (value == null) {
              return 'Please select a denim type';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: CustomTextField(
                controller: _rollIdController,
                label: 'Roll ID',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter roll ID';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            CustomButton(
              text: 'Generate',
              onPressed: _generateRollId,
              variant: ButtonVariant.outlined,
              size: ButtonSize.small,
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _colorController,
          label: 'Color',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter color';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildMeasurementsSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _widthController,
                label: 'Width (inches)',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter width';
                  }
                  final width = double.tryParse(value);
                  if (width == null || width <= 0) {
                    return 'Please enter valid width';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _gsmController,
                label: 'GSM',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter GSM';
                  }
                  final gsm = double.tryParse(value);
                  if (gsm == null || gsm <= 0) {
                    return 'Please enter valid GSM';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _lengthController,
                label: 'Length',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter length';
                  }
                  final length = double.tryParse(value);
                  if (length == null || length <= 0) {
                    return 'Please enter valid length';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomDropdown<String>(
                label: 'Unit',
                value: _selectedUnit,
                items: const [
                  DropdownMenuItem(value: 'yards', child: Text('Yards')),
                  DropdownMenuItem(value: 'meters', child: Text('Meters')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedUnit = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _ratePerUnitController,
                label: 'Rate per Unit (\$)',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter rate per unit';
                  }
                  final rate = double.tryParse(value);
                  if (rate == null || rate <= 0) {
                    return 'Please enter valid rate';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Cost',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '\$${_totalCost.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Column(
      children: [
        CustomTextField(
          controller: _remarksController,
          label: 'Remarks (Optional)',
          maxLines: 3,
          textInputAction: TextInputAction.newline,
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'Create Purchase Record',
        onPressed: _isLoading ? null : _submitForm,
        isLoading: _isLoading,
        size: ButtonSize.large,
      ),
    );
  }
}
