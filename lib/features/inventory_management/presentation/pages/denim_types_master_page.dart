import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../domain/requests/denim_inventory_requests.dart';

class DenimTypesMasterPage extends StatefulWidget {
  const DenimTypesMasterPage({super.key});

  @override
  State<DenimTypesMasterPage> createState() => _DenimTypesMasterPageState();
}

class _DenimTypesMasterPageState extends State<DenimTypesMasterPage> {
  final _searchController = TextEditingController();
  bool _showInactiveTypes = false;

  // Mock data - in real app, this would come from repository
  final List<DenimType> _denimTypes = [
    DenimType(
      id: '1',
      denimCode: 'DN001',
      type: '12oz Indigo',
      description: 'Premium 12oz indigo denim for jeans and jackets',
      gsm: 320,
      width: 58,
      supplierId: 'sup1',
      supplierName: 'Textile Mills Ltd.',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),
    DenimType(
      id: '2',
      denimCode: 'DN002',
      type: 'Stretch Denim',
      description: 'Stretch denim with 2% elastane for comfort fit',
      gsm: 280,
      width: 60,
      supplierId: 'sup2',
      supplierName: 'Premium Fabrics Inc.',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now(),
    ),
    DenimType(
      id: '3',
      denimCode: 'DN003',
      type: 'Raw Denim',
      description: 'Unwashed raw denim for premium jeans',
      gsm: 350,
      width: 56,
      supplierId: 'sup3',
      supplierName: 'Heritage Denim Co.',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
    ),
    DenimType(
      id: '4',
      denimCode: 'DN004',
      type: 'Light Weight Denim',
      description: 'Light weight denim for summer wear',
      gsm: 220,
      width: 58,
      supplierId: 'sup1',
      supplierName: 'Textile Mills Ltd.',
      isActive: false,
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<DenimType> get _filteredTypes {
    return _denimTypes.where((type) {
      // Active/Inactive filter
      if (!_showInactiveTypes && !type.isActive) {
        return false;
      }

      // Search filter
      final searchQuery = _searchController.text.toLowerCase();
      if (searchQuery.isNotEmpty) {
        return type.denimCode.toLowerCase().contains(searchQuery) ||
               type.type.toLowerCase().contains(searchQuery) ||
               type.description.toLowerCase().contains(searchQuery) ||
               (type.supplierName?.toLowerCase().contains(searchQuery) ?? false);
      }

      return true;
    }).toList();
  }

  Future<void> _showAddEditDialog({DenimType? denimType}) async {
    await showDialog(
      context: context,
      builder: (context) => _DenimTypeDialog(denimType: denimType),
    );
    // In real app, refresh data from repository
    setState(() {});
  }

  Future<void> _toggleActiveStatus(DenimType denimType) async {
    // In real app, this would call repository to update status
    setState(() {
      final index = _denimTypes.indexWhere((t) => t.id == denimType.id);
      if (index != -1) {
        // This is just for demo - in real app, you'd create a new instance
        // _denimTypes[index] = denimType.copyWith(isActive: !denimType.isActive);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          denimType.isActive 
              ? 'Denim type deactivated successfully'
              : 'Denim type activated successfully',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _deleteDenimType(DenimType denimType) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Denim Type'),
        content: Text(
          'Are you sure you want to delete "${denimType.denimCode} - ${denimType.type}"?\n\n'
          'This action cannot be undone and may affect existing inventory records.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // In real app, this would call repository to delete
      setState(() {
        _denimTypes.removeWhere((t) => t.id == denimType.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Denim type deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredTypes = _filteredTypes;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Denim Types Master',
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEditDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildSummaryCards(filteredTypes),
          Expanded(
            child: _buildDenimTypesList(filteredTypes),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          CustomTextField(
            controller: _searchController,
            label: 'Search by code, type, description, or supplier',
            prefixIcon: const Icon(Icons.search),
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Inactive Types'),
                  value: _showInactiveTypes,
                  onChanged: (value) => setState(() => _showInactiveTypes = value ?? false),
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _showInactiveTypes = false;
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(List<DenimType> types) {
    final activeTypes = types.where((t) => t.isActive).length;
    final inactiveTypes = types.where((t) => !t.isActive).length;
    final totalSuppliers = types.map((t) => t.supplierId).where((s) => s != null).toSet().length;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Total Types',
              types.length.toString(),
              Icons.category,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Active',
              activeTypes.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Inactive',
              inactiveTypes.toString(),
              Icons.cancel,
              Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'Suppliers',
              totalSuppliers.toString(),
              Icons.business,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
