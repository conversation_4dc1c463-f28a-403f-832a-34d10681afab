import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_dropdown.dart';
import '../../domain/entities/denim_inventory_entities.dart';
import '../../domain/requests/denim_inventory_requests.dart';

class DenimReturnPage extends StatefulWidget {
  const DenimReturnPage({super.key});

  @override
  State<DenimReturnPage> createState() => _DenimReturnPageState();
}

class _DenimReturnPageState extends State<DenimReturnPage> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Form controllers
  final _returnDateController = TextEditingController();
  final _rollIdController = TextEditingController();
  final _supplierNameController = TextEditingController();
  final _reasonController = TextEditingController();
  final _quantityController = TextEditingController();
  final _debitNoteController = TextEditingController();
  final _remarksController = TextEditingController();

  // Form state
  DateTime _selectedDate = DateTime.now();
  String _selectedUnit = 'yards';
  String _selectedReason = 'Damaged';
  bool _isLoading = false;
  DenimRoll? _selectedRoll;

  // Mock data - in real app, this would come from repository
  final List<DenimRoll> _availableRolls = [
    DenimRoll(
      id: '1',
      rollId: 'DN001-20241201-001',
      denimTypeId: '1',
      denimCode: 'DN001',
      denimType: '12oz Indigo',
      color: 'Navy Blue',
      width: 58,
      totalPurchasedQty: 100,
      returnedQty: 0,
      usedQty: 20,
      balanceQty: 80,
      status: DenimRollStatus.inStock,
      gsm: 320,
      supplierName: 'Textile Mills Ltd.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    DenimRoll(
      id: '2',
      rollId: 'DN002-20241201-001',
      denimTypeId: '2',
      denimCode: 'DN002',
      denimType: 'Stretch Denim',
      color: 'Black',
      width: 60,
      totalPurchasedQty: 150,
      returnedQty: 0,
      usedQty: 30,
      balanceQty: 120,
      status: DenimRollStatus.inStock,
      gsm: 280,
      supplierName: 'Premium Fabrics Inc.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  final List<String> _returnReasons = [
    'Damaged',
    'Quality Issues',
    'Wrong Specification',
    'Color Mismatch',
    'Width Variation',
    'GSM Variation',
    'Defective Material',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _returnDateController.text = DateFormat('dd/MM/yyyy').format(_selectedDate);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _returnDateController.dispose();
    _rollIdController.dispose();
    _supplierNameController.dispose();
    _reasonController.dispose();
    _quantityController.dispose();
    _debitNoteController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
        _returnDateController.text = DateFormat('dd/MM/yyyy').format(date);
      });
    }
  }

  void _onRollSelected(DenimRoll? roll) {
    setState(() {
      _selectedRoll = roll;
      if (roll != null) {
        _rollIdController.text = roll.rollId;
        _supplierNameController.text = roll.supplierName ?? '';
        _selectedUnit = roll.unit;
      } else {
        _rollIdController.clear();
        _supplierNameController.clear();
      }
    });
  }

  Future<void> _generateDebitNote() async {
    if (_selectedRoll == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a roll first')),
      );
      return;
    }

    // In real app, this would call the repository to generate debit note number
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final debitNote = 'DN-$dateStr-001';
    
    setState(() {
      _debitNoteController.text = debitNote;
    });
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedRoll == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a roll')),
      );
      return;
    }

    final quantity = double.parse(_quantityController.text);
    if (quantity > _selectedRoll!.balanceQty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Return quantity cannot exceed available balance (${_selectedRoll!.balanceQty} ${_selectedRoll!.unit})'
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CreateReturnRequest(
        returnDate: _selectedDate,
        rollId: _selectedRoll!.rollId,
        supplierName: _supplierNameController.text.trim(),
        supplierId: _selectedRoll!.supplierId,
        reasonForReturn: _selectedReason == 'Other' 
            ? _reasonController.text.trim() 
            : _selectedReason,
        quantityReturned: quantity,
        unit: _selectedUnit,
        debitNoteNo: _debitNoteController.text.trim(),
        remarks: _remarksController.text.trim().isEmpty 
            ? null 
            : _remarksController.text.trim(),
      );

      // In real app, this would call the repository to create return record
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Return record created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating return record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Return Denim Purchase',
        backgroundColor: Colors.red,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Return Information'),
              const SizedBox(height: 16),
              _buildReturnInfoSection(),
              const SizedBox(height: 24),
              
              _buildSectionHeader('Roll Selection'),
              const SizedBox(height: 16),
              _buildRollSelectionSection(),
              const SizedBox(height: 24),
              
              if (_selectedRoll != null) ...[
                _buildSectionHeader('Roll Details'),
                const SizedBox(height: 16),
                _buildRollDetailsCard(),
                const SizedBox(height: 24),
              ],
              
              _buildSectionHeader('Return Details'),
              const SizedBox(height: 16),
              _buildReturnDetailsSection(),
              const SizedBox(height: 24),
              
              _buildSectionHeader('Additional Information'),
              const SizedBox(height: 16),
              _buildAdditionalInfoSection(),
              const SizedBox(height: 32),
              
              _buildSubmitButton(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.red,
      ),
    );
  }

  Widget _buildReturnInfoSection() {
    return Column(
      children: [
        CustomTextField(
          controller: _returnDateController,
          label: 'Return Date',
          readOnly: true,
          suffixIcon: IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select return date';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: CustomTextField(
                controller: _debitNoteController,
                label: 'Debit Note Number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter debit note number';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            CustomButton(
              text: 'Generate',
              onPressed: _generateDebitNote,
              variant: ButtonVariant.outlined,
              size: ButtonSize.small,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRollSelectionSection() {
    return Column(
      children: [
        CustomDropdown<DenimRoll>(
          label: 'Select Roll',
          value: _selectedRoll,
          items: _availableRolls.map((roll) => DropdownMenuItem(
            value: roll,
            child: Text('${roll.rollId} - ${roll.denimType} (${roll.color})'),
          )).toList(),
          onChanged: _onRollSelected,
          validator: (value) {
            if (value == null) {
              return 'Please select a roll';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _rollIdController,
          label: 'Roll ID',
          readOnly: true,
          enabled: false,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _supplierNameController,
          label: 'Supplier Name',
          readOnly: true,
          enabled: false,
        ),
      ],
    );
  }

  Widget _buildRollDetailsCard() {
    if (_selectedRoll == null) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Roll Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Denim Type', _selectedRoll!.denimType),
            _buildInfoRow('Color', _selectedRoll!.color),
            _buildInfoRow('Width', '${_selectedRoll!.width} inches'),
            _buildInfoRow('GSM', _selectedRoll!.gsm.toString()),
            _buildInfoRow('Total Purchased', '${_selectedRoll!.totalPurchasedQty} ${_selectedRoll!.unit}'),
            _buildInfoRow('Used Quantity', '${_selectedRoll!.usedQty} ${_selectedRoll!.unit}'),
            _buildInfoRow('Returned Quantity', '${_selectedRoll!.returnedQty} ${_selectedRoll!.unit}'),
            _buildInfoRow('Available Balance', '${_selectedRoll!.balanceQty} ${_selectedRoll!.unit}',
                isHighlighted: true),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isHighlighted = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                color: isHighlighted ? AppColors.primary : Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReturnDetailsSection() {
    return Column(
      children: [
        CustomDropdown<String>(
          label: 'Reason for Return',
          value: _selectedReason,
          items: _returnReasons.map((reason) => DropdownMenuItem(
            value: reason,
            child: Text(reason),
          )).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedReason = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        if (_selectedReason == 'Other') ...[
          CustomTextField(
            controller: _reasonController,
            label: 'Specify Reason',
            validator: (value) {
              if (_selectedReason == 'Other' && (value == null || value.isEmpty)) {
                return 'Please specify the reason';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
        ],
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _quantityController,
                label: 'Quantity to Return',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter quantity';
                  }
                  final quantity = double.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Please enter valid quantity';
                  }
                  if (_selectedRoll != null && quantity > _selectedRoll!.balanceQty) {
                    return 'Cannot exceed available balance';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomDropdown<String>(
                label: 'Unit',
                value: _selectedUnit,
                items: const [
                  DropdownMenuItem(value: 'yards', child: Text('Yards')),
                  DropdownMenuItem(value: 'meters', child: Text('Meters')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedUnit = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Column(
      children: [
        CustomTextField(
          controller: _remarksController,
          label: 'Remarks (Optional)',
          maxLines: 3,
          textInputAction: TextInputAction.newline,
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'Create Return Record',
        onPressed: _isLoading ? null : _submitForm,
        isLoading: _isLoading,
        size: ButtonSize.large,
        backgroundColor: Colors.red,
      ),
    );
  }
}
