import 'package:equatable/equatable.dart';
import '../../../../core/domain/entities/base_entity.dart';
import '../../../../shared/enums/common_enums.dart';

/// Denim Type entity for master list
class DenimType extends BaseEntity {
  final String denimCode;
  final String type;
  final String description;
  final double gsm;
  final double width;
  final String? supplierId;
  final String? supplierName;
  final bool isActive;

  const DenimType({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
    this.isActive = true,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        denimCode,
        type,
        description,
        gsm,
        width,
        supplierId,
        supplierName,
        isActive,
      ];
}

/// Denim Roll entity for inventory tracking
class DenimRoll extends BaseEntity {
  final String rollId;
  final String denimTypeId;
  final String denimCode;
  final String denimType;
  final String color;
  final double width;
  final double totalPurchasedQty;
  final double returnedQty;
  final double usedQty;
  final double balanceQty;
  final String? location;
  final DenimRollStatus status;
  final String unit; // yards/meters
  final double gsm;
  final String? supplierId;
  final String? supplierName;
  final Map<String, dynamic> metadata;

  const DenimRoll({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required this.rollId,
    required this.denimTypeId,
    required this.denimCode,
    required this.denimType,
    required this.color,
    required this.width,
    required this.totalPurchasedQty,
    this.returnedQty = 0.0,
    this.usedQty = 0.0,
    required this.balanceQty,
    this.location,
    required this.status,
    this.unit = 'yards',
    required this.gsm,
    this.supplierId,
    this.supplierName,
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        rollId,
        denimTypeId,
        denimCode,
        denimType,
        color,
        width,
        totalPurchasedQty,
        returnedQty,
        usedQty,
        balanceQty,
        location,
        status,
        unit,
        gsm,
        supplierId,
        supplierName,
        metadata,
      ];

  /// Calculate available quantity
  double get availableQty => totalPurchasedQty - returnedQty - usedQty;

  /// Check if roll is in stock
  bool get isInStock => status == DenimRollStatus.inStock && balanceQty > 0;

  /// Check if roll is sent to cutting
  bool get isSentToCutting => status == DenimRollStatus.sentToCutting;
}

/// Purchase Record entity
class DenimPurchaseRecord extends BaseEntity {
  final int srNo;
  final DateTime purchaseDate;
  final String supplierName;
  final String? supplierId;
  final String rollId;
  final String denimTypeId;
  final String denimType;
  final String color;
  final double width;
  final double length;
  final String unit; // yards/meters
  final double gsm;
  final double ratePerUnit;
  final double totalCost;
  final String invoiceNo;
  final String? remarks;
  final PurchaseStatus status;

  const DenimPurchaseRecord({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required this.srNo,
    required this.purchaseDate,
    required this.supplierName,
    this.supplierId,
    required this.rollId,
    required this.denimTypeId,
    required this.denimType,
    required this.color,
    required this.width,
    required this.length,
    this.unit = 'yards',
    required this.gsm,
    required this.ratePerUnit,
    required this.totalCost,
    required this.invoiceNo,
    this.remarks,
    this.status = PurchaseStatus.completed,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        srNo,
        purchaseDate,
        supplierName,
        supplierId,
        rollId,
        denimTypeId,
        denimType,
        color,
        width,
        length,
        unit,
        gsm,
        ratePerUnit,
        totalCost,
        invoiceNo,
        remarks,
        status,
      ];
}

/// Return Purchase Record entity
class DenimReturnRecord extends BaseEntity {
  final int srNo;
  final DateTime returnDate;
  final String rollId;
  final String supplierName;
  final String? supplierId;
  final String reasonForReturn;
  final double quantityReturned;
  final String unit; // yards/meters
  final String debitNoteNo;
  final String? remarks;
  final ReturnStatus status;
  final double? refundAmount;

  const DenimReturnRecord({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    String? createdBy,
    String? updatedBy,
    required this.srNo,
    required this.returnDate,
    required this.rollId,
    required this.supplierName,
    this.supplierId,
    required this.reasonForReturn,
    required this.quantityReturned,
    this.unit = 'yards',
    required this.debitNoteNo,
    this.remarks,
    this.status = ReturnStatus.pending,
    this.refundAmount,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        srNo,
        returnDate,
        rollId,
        supplierName,
        supplierId,
        reasonForReturn,
        quantityReturned,
        unit,
        debitNoteNo,
        remarks,
        status,
        refundAmount,
      ];
}

/// Enums for denim inventory
enum DenimRollStatus {
  inStock,
  sentToCutting,
  returned,
  damaged,
  outOfStock,
}

enum PurchaseStatus {
  pending,
  completed,
  cancelled,
  partiallyReceived,
}

enum ReturnStatus {
  pending,
  approved,
  rejected,
  completed,
}

/// Extensions for enum display
extension DenimRollStatusExtension on DenimRollStatus {
  String get displayName {
    switch (this) {
      case DenimRollStatus.inStock:
        return 'In Stock';
      case DenimRollStatus.sentToCutting:
        return 'Sent to Cutting';
      case DenimRollStatus.returned:
        return 'Returned';
      case DenimRollStatus.damaged:
        return 'Damaged';
      case DenimRollStatus.outOfStock:
        return 'Out of Stock';
    }
  }

  String get value => toString().split('.').last;
}

extension PurchaseStatusExtension on PurchaseStatus {
  String get displayName {
    switch (this) {
      case PurchaseStatus.pending:
        return 'Pending';
      case PurchaseStatus.completed:
        return 'Completed';
      case PurchaseStatus.cancelled:
        return 'Cancelled';
      case PurchaseStatus.partiallyReceived:
        return 'Partially Received';
    }
  }

  String get value => toString().split('.').last;
}

extension ReturnStatusExtension on ReturnStatus {
  String get displayName {
    switch (this) {
      case ReturnStatus.pending:
        return 'Pending';
      case ReturnStatus.approved:
        return 'Approved';
      case ReturnStatus.rejected:
        return 'Rejected';
      case ReturnStatus.completed:
        return 'Completed';
    }
  }

  String get value => toString().split('.').last;
}
