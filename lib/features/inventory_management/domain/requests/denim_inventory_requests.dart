import 'package:equatable/equatable.dart';
import '../entities/denim_inventory_entities.dart';

/// Request to create a new denim type
class CreateDenimTypeRequest extends Equatable {
  final String denimCode;
  final String type;
  final String description;
  final double gsm;
  final double width;
  final String? supplierId;
  final String? supplierName;

  const CreateDenimTypeRequest({
    required this.denimCode,
    required this.type,
    required this.description,
    required this.gsm,
    required this.width,
    this.supplierId,
    this.supplierName,
  });

  @override
  List<Object?> get props => [
        denimCode,
        type,
        description,
        gsm,
        width,
        supplierId,
        supplierName,
      ];
}

/// Request to update a denim type
class UpdateDenimTypeRequest extends Equatable {
  final String id;
  final String? denimCode;
  final String? type;
  final String? description;
  final double? gsm;
  final double? width;
  final String? supplierId;
  final String? supplierName;
  final bool? isActive;

  const UpdateDenimTypeRequest({
    required this.id,
    this.denimCode,
    this.type,
    this.description,
    this.gsm,
    this.width,
    this.supplierId,
    this.supplierName,
    this.isActive,
  });

  @override
  List<Object?> get props => [
        id,
        denimCode,
        type,
        description,
        gsm,
        width,
        supplierId,
        supplierName,
        isActive,
      ];
}

/// Request to create a purchase record
class CreatePurchaseRequest extends Equatable {
  final DateTime purchaseDate;
  final String supplierName;
  final String? supplierId;
  final String rollId;
  final String denimTypeId;
  final String color;
  final double width;
  final double length;
  final String unit;
  final double gsm;
  final double ratePerUnit;
  final String invoiceNo;
  final String? remarks;

  const CreatePurchaseRequest({
    required this.purchaseDate,
    required this.supplierName,
    this.supplierId,
    required this.rollId,
    required this.denimTypeId,
    required this.color,
    required this.width,
    required this.length,
    this.unit = 'yards',
    required this.gsm,
    required this.ratePerUnit,
    required this.invoiceNo,
    this.remarks,
  });

  double get totalCost => length * ratePerUnit;

  @override
  List<Object?> get props => [
        purchaseDate,
        supplierName,
        supplierId,
        rollId,
        denimTypeId,
        color,
        width,
        length,
        unit,
        gsm,
        ratePerUnit,
        invoiceNo,
        remarks,
      ];
}

/// Request to create a return record
class CreateReturnRequest extends Equatable {
  final DateTime returnDate;
  final String rollId;
  final String supplierName;
  final String? supplierId;
  final String reasonForReturn;
  final double quantityReturned;
  final String unit;
  final String debitNoteNo;
  final String? remarks;

  const CreateReturnRequest({
    required this.returnDate,
    required this.rollId,
    required this.supplierName,
    this.supplierId,
    required this.reasonForReturn,
    required this.quantityReturned,
    this.unit = 'yards',
    required this.debitNoteNo,
    this.remarks,
  });

  @override
  List<Object?> get props => [
        returnDate,
        rollId,
        supplierName,
        supplierId,
        reasonForReturn,
        quantityReturned,
        unit,
        debitNoteNo,
        remarks,
      ];
}

/// Request to update stock quantities
class UpdateStockRequest extends Equatable {
  final String rollId;
  final double? usedQty;
  final double? returnedQty;
  final DenimRollStatus? status;
  final String? location;
  final String reason;

  const UpdateStockRequest({
    required this.rollId,
    this.usedQty,
    this.returnedQty,
    this.status,
    this.location,
    required this.reason,
  });

  @override
  List<Object?> get props => [
        rollId,
        usedQty,
        returnedQty,
        status,
        location,
        reason,
      ];
}

/// Request to filter denim inventory
class DenimInventoryFilterRequest extends Equatable {
  final String? denimTypeId;
  final String? color;
  final DenimRollStatus? status;
  final String? location;
  final String? supplierId;
  final double? minWidth;
  final double? maxWidth;
  final double? minGsm;
  final double? maxGsm;
  final bool? hasStock;
  final DateTime? purchaseDateFrom;
  final DateTime? purchaseDateTo;

  const DenimInventoryFilterRequest({
    this.denimTypeId,
    this.color,
    this.status,
    this.location,
    this.supplierId,
    this.minWidth,
    this.maxWidth,
    this.minGsm,
    this.maxGsm,
    this.hasStock,
    this.purchaseDateFrom,
    this.purchaseDateTo,
  });

  @override
  List<Object?> get props => [
        denimTypeId,
        color,
        status,
        location,
        supplierId,
        minWidth,
        maxWidth,
        minGsm,
        maxGsm,
        hasStock,
        purchaseDateFrom,
        purchaseDateTo,
      ];
}

/// Request to search denim inventory
class SearchDenimInventoryRequest extends Equatable {
  final String query;
  final DenimInventoryFilterRequest? filter;
  final int? limit;
  final String? lastDocumentId;

  const SearchDenimInventoryRequest({
    required this.query,
    this.filter,
    this.limit = 20,
    this.lastDocumentId,
  });

  @override
  List<Object?> get props => [
        query,
        filter,
        limit,
        lastDocumentId,
      ];
}

/// Request for bulk stock update
class BulkStockUpdateRequest extends Equatable {
  final List<UpdateStockRequest> updates;
  final String reason;

  const BulkStockUpdateRequest({
    required this.updates,
    required this.reason,
  });

  @override
  List<Object?> get props => [updates, reason];
}

/// Request for stock transfer
class StockTransferRequest extends Equatable {
  final String rollId;
  final String fromLocation;
  final String toLocation;
  final double quantity;
  final String reason;
  final String? transferNote;

  const StockTransferRequest({
    required this.rollId,
    required this.fromLocation,
    required this.toLocation,
    required this.quantity,
    required this.reason,
    this.transferNote,
  });

  @override
  List<Object?> get props => [
        rollId,
        fromLocation,
        toLocation,
        quantity,
        reason,
        transferNote,
      ];
}
