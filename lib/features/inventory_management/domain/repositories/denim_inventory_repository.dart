import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/api_response.dart';
import '../entities/denim_inventory_entities.dart';
import '../requests/denim_inventory_requests.dart';

/// Repository interface for denim inventory management
abstract class DenimInventoryRepository {
  // Denim Types Management
  Future<Either<Failure, List<DenimType>>> getDenimTypes({
    bool activeOnly = true,
  });

  Future<Either<Failure, DenimType>> getDenimTypeById(String id);

  Future<Either<Failure, DenimType>> createDenimType(
    CreateDenimTypeRequest request,
  );

  Future<Either<Failure, DenimType>> updateDenimType(
    UpdateDenimTypeRequest request,
  );

  Future<Either<Failure, void>> deleteDenimType(String id);

  // Denim Rolls Management
  Future<Either<Failure, List<DenimRoll>>> getDenimRolls({
    DenimInventoryFilterRequest? filter,
    int? limit,
    String? lastDocumentId,
  });

  Future<Either<Failure, DenimRoll>> getDenimRollById(String id);

  Future<Either<Failure, DenimRoll?>> getDenimRollByRollId(String rollId);

  Future<Either<Failure, List<DenimRoll>>> searchDenimRolls(
    SearchDenimInventoryRequest request,
  );

  // Purchase Management
  Future<Either<Failure, DenimPurchaseRecord>> createPurchaseRecord(
    CreatePurchaseRequest request,
  );

  Future<Either<Failure, List<DenimPurchaseRecord>>> getPurchaseRecords({
    String? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    String? lastDocumentId,
  });

  Future<Either<Failure, DenimPurchaseRecord>> getPurchaseRecordById(String id);

  Future<Either<Failure, List<DenimPurchaseRecord>>> getPurchaseRecordsByRollId(
    String rollId,
  );

  // Return Management
  Future<Either<Failure, DenimReturnRecord>> createReturnRecord(
    CreateReturnRequest request,
  );

  Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecords({
    String? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
    ReturnStatus? status,
    int? limit,
    String? lastDocumentId,
  });

  Future<Either<Failure, DenimReturnRecord>> getReturnRecordById(String id);

  Future<Either<Failure, List<DenimReturnRecord>>> getReturnRecordsByRollId(
    String rollId,
  );

  Future<Either<Failure, DenimReturnRecord>> updateReturnStatus(
    String id,
    ReturnStatus status,
    String? remarks,
  );

  // Stock Management
  Future<Either<Failure, DenimRoll>> updateStock(
    UpdateStockRequest request,
  );

  Future<Either<Failure, List<DenimRoll>>> bulkUpdateStock(
    BulkStockUpdateRequest request,
  );

  Future<Either<Failure, DenimRoll>> transferStock(
    StockTransferRequest request,
  );

  // Analytics and Reporting
  Future<Either<Failure, Map<String, dynamic>>> getStockSummary({
    String? denimTypeId,
    String? location,
  });

  Future<Either<Failure, List<DenimRoll>>> getLowStockRolls({
    double threshold = 10.0,
  });

  Future<Either<Failure, Map<String, dynamic>>> getPurchaseAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
    String? supplierId,
  });

  Future<Either<Failure, Map<String, dynamic>>> getReturnAnalytics({
    DateTime? fromDate,
    DateTime? toDate,
    String? supplierId,
  });

  // Real-time streams
  Stream<List<DenimRoll>> watchDenimRolls({
    DenimInventoryFilterRequest? filter,
  });

  Stream<List<DenimType>> watchDenimTypes();

  Stream<DenimRoll?> watchDenimRollByRollId(String rollId);

  Stream<List<DenimPurchaseRecord>> watchPurchaseRecords({
    String? supplierId,
    DateTime? fromDate,
  });

  Stream<List<DenimReturnRecord>> watchReturnRecords({
    String? supplierId,
    ReturnStatus? status,
  });

  // Utility methods
  Future<Either<Failure, String>> generateRollId(String denimTypeId);

  Future<Either<Failure, int>> getNextSerialNumber(String type);

  Future<Either<Failure, bool>> isRollIdUnique(String rollId);

  Future<Either<Failure, List<String>>> getAvailableLocations();

  Future<Either<Failure, List<String>>> getAvailableColors();

  Future<Either<Failure, Map<String, dynamic>>> validatePurchaseData(
    CreatePurchaseRequest request,
  );

  Future<Either<Failure, Map<String, dynamic>>> validateReturnData(
    CreateReturnRequest request,
  );
}
