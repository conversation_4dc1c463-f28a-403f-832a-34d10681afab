import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/inventory_entities.dart';
import '../entities/supplier_entities.dart';

/// Inventory repository interface
abstract class InventoryRepository {
  /// Get inventory items with filtering and pagination
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getInventoryItems({
    InventoryFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get inventory item by ID
  Future<Either<Failure, ApiResponse<InventoryItem>>> getInventoryItemById(
    String itemId,
  );

  /// Create inventory item
  Future<Either<Failure, ApiResponse<InventoryItem>>> createInventoryItem(
    CreateInventoryItemRequest request,
  );

  /// Update inventory item
  Future<Either<Failure, ApiResponse<InventoryItem>>> updateInventoryItem(
    UpdateInventoryItemRequest request,
  );

  /// Delete inventory item
  Future<Either<Failure, ApiVoidResponse>> deleteInventoryItem(
    String itemId,
    String? reason,
  );

  /// Update stock level
  Future<Either<Failure, ApiResponse<InventoryItem>>> updateStockLevel(
    String itemId,
    double newStock,
    String reason,
  );

  /// Adjust stock
  Future<Either<Failure, ApiResponse<StockMovement>>> adjustStock(
    AdjustStockRequest request,
  );

  /// Reserve stock
  Future<Either<Failure, ApiResponse<InventoryItem>>> reserveStock(
    String itemId,
    double quantity,
    String reason,
    String? orderId,
  );

  /// Release reserved stock
  Future<Either<Failure, ApiResponse<InventoryItem>>> releaseReservedStock(
    String itemId,
    double quantity,
    String reason,
  );

  /// Get stock movements
  Future<Either<Failure, ApiListResponse<StockMovement>>> getStockMovements({
    StockMovementFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get stock movements for item
  Future<Either<Failure, ApiListResponse<StockMovement>>> getStockMovementsForItem(
    String itemId, {
    PaginationParams? pagination,
  });

  /// Get low stock items
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getLowStockItems({
    PaginationParams? pagination,
  });

  /// Get out of stock items
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getOutOfStockItems({
    PaginationParams? pagination,
  });

  /// Get overstocked items
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getOverstockedItems({
    PaginationParams? pagination,
  });

  /// Get expiring items
  Future<Either<Failure, ApiListResponse<InventoryItem>>> getExpiringItems({
    int daysAhead = 30,
    PaginationParams? pagination,
  });

  /// Search inventory items
  Future<Either<Failure, ApiListResponse<InventoryItem>>> searchInventoryItems({
    required String query,
    InventoryFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get inventory statistics
  Future<Either<Failure, InventoryStatistics>> getInventoryStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
  });

  /// Get inventory valuation
  Future<Either<Failure, InventoryValuation>> getInventoryValuation({
    DateTime? asOfDate,
    String? categoryId,
  });

  /// Generate reorder suggestions
  Future<Either<Failure, List<ReorderSuggestion>>> generateReorderSuggestions({
    String? categoryId,
    int daysAhead = 30,
  });

  /// Create automatic reorder
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> createAutomaticReorder(
    List<String> itemIds,
  );

  /// Export inventory data
  Future<Either<Failure, String>> exportInventoryData({
    InventoryFilterCriteria? filter,
    String format = 'csv',
  });

  /// Import inventory data
  Future<Either<Failure, ApiResponse<InventoryImportResult>>> importInventoryData(
    String filePath, {
    bool validateOnly = false,
  });

  /// Bulk update inventory
  Future<Either<Failure, ApiResponse<BulkUpdateResult>>> bulkUpdateInventory(
    List<BulkUpdateInventoryRequest> requests,
  );
}

/// Bill of Materials repository interface
abstract class BOMRepository {
  /// Get BOMs with filtering and pagination
  Future<Either<Failure, ApiListResponse<BillOfMaterials>>> getBOMs({
    BOMFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get BOM by ID
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> getBOMById(
    String bomId,
  );

  /// Create BOM
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> createBOM(
    CreateBOMRequest request,
  );

  /// Update BOM
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> updateBOM(
    UpdateBOMRequest request,
  );

  /// Delete BOM
  Future<Either<Failure, ApiVoidResponse>> deleteBOM(
    String bomId,
    String? reason,
  );

  /// Approve BOM
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> approveBOM(
    String bomId,
    String approverId,
  );

  /// Activate BOM
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> activateBOM(
    String bomId,
  );

  /// Deactivate BOM
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> deactivateBOM(
    String bomId,
    String reason,
  );

  /// Get BOM for product
  Future<Either<Failure, ApiResponse<BillOfMaterials>>> getBOMForProduct(
    String productId,
  );

  /// Calculate material requirements
  Future<Either<Failure, List<MaterialRequirement>>> calculateMaterialRequirements(
    String bomId,
    double quantity,
  );

  /// Check material availability
  Future<Either<Failure, MaterialAvailabilityReport>> checkMaterialAvailability(
    String bomId,
    double quantity,
  );
}

/// Purchase Order repository interface
abstract class PurchaseOrderRepository {
  /// Get purchase orders with filtering and pagination
  Future<Either<Failure, ApiListResponse<PurchaseOrder>>> getPurchaseOrders({
    PurchaseOrderFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get purchase order by ID
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> getPurchaseOrderById(
    String poId,
  );

  /// Create purchase order
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> createPurchaseOrder(
    CreatePurchaseOrderRequest request,
  );

  /// Update purchase order
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> updatePurchaseOrder(
    UpdatePurchaseOrderRequest request,
  );

  /// Delete purchase order
  Future<Either<Failure, ApiVoidResponse>> deletePurchaseOrder(
    String poId,
    String? reason,
  );

  /// Approve purchase order
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> approvePurchaseOrder(
    String poId,
    String approverId,
  );

  /// Send purchase order
  Future<Either<Failure, ApiVoidResponse>> sendPurchaseOrder(
    String poId,
  );

  /// Receive purchase order items
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> receivePurchaseOrderItems(
    ReceivePurchaseOrderRequest request,
  );

  /// Cancel purchase order
  Future<Either<Failure, ApiResponse<PurchaseOrder>>> cancelPurchaseOrder(
    String poId,
    String reason,
  );

  /// Get overdue purchase orders
  Future<Either<Failure, ApiListResponse<PurchaseOrder>>> getOverduePurchaseOrders({
    PaginationParams? pagination,
  });

  /// Get purchase order statistics
  Future<Either<Failure, PurchaseOrderStatistics>> getPurchaseOrderStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? supplierId,
  });
}

/// Supplier repository interface
abstract class SupplierRepository {
  /// Get suppliers with filtering and pagination
  Future<Either<Failure, ApiListResponse<Supplier>>> getSuppliers({
    SupplierFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Get supplier by ID
  Future<Either<Failure, ApiResponse<Supplier>>> getSupplierById(
    String supplierId,
  );

  /// Create supplier
  Future<Either<Failure, ApiResponse<Supplier>>> createSupplier(
    CreateSupplierRequest request,
  );

  /// Update supplier
  Future<Either<Failure, ApiResponse<Supplier>>> updateSupplier(
    UpdateSupplierRequest request,
  );

  /// Delete supplier
  Future<Either<Failure, ApiVoidResponse>> deleteSupplier(
    String supplierId,
    String? reason,
  );

  /// Get supplier quotations
  Future<Either<Failure, ApiListResponse<SupplierQuotation>>> getSupplierQuotations({
    QuotationFilterCriteria? filter,
    PaginationParams? pagination,
  });

  /// Create supplier quotation
  Future<Either<Failure, ApiResponse<SupplierQuotation>>> createSupplierQuotation(
    CreateQuotationRequest request,
  );

  /// Update supplier rating
  Future<Either<Failure, ApiResponse<Supplier>>> updateSupplierRating(
    String supplierId,
    SupplierRating rating,
  );

  /// Get supplier statistics
  Future<Either<Failure, SupplierStatistics>> getSupplierStatistics(
    String supplierId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Search suppliers
  Future<Either<Failure, ApiListResponse<Supplier>>> searchSuppliers({
    required String query,
    SupplierFilterCriteria? filter,
    PaginationParams? pagination,
  });
}

// Filter criteria classes

/// Inventory filter criteria
class InventoryFilterCriteria {
  final ItemCategory? category;
  final ItemType? type;
  final ItemStatus? status;
  final StockStatus? stockStatus;
  final String? supplierId;
  final String? location;
  final bool? isLowStock;
  final bool? isOutOfStock;
  final bool? isExpiring;
  final double? minStock;
  final double? maxStock;
  final List<String>? tags;

  const InventoryFilterCriteria({
    this.category,
    this.type,
    this.status,
    this.stockStatus,
    this.supplierId,
    this.location,
    this.isLowStock,
    this.isOutOfStock,
    this.isExpiring,
    this.minStock,
    this.maxStock,
    this.tags,
  });
}

/// Stock movement filter criteria
class StockMovementFilterCriteria {
  final String? itemId;
  final MovementType? movementType;
  final String? userId;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final String? orderId;
  final String? supplierId;

  const StockMovementFilterCriteria({
    this.itemId,
    this.movementType,
    this.userId,
    this.dateFrom,
    this.dateTo,
    this.orderId,
    this.supplierId,
  });
}

/// BOM filter criteria
class BOMFilterCriteria {
  final String? productId;
  final BOMStatus? status;
  final String? createdBy;
  final DateTime? effectiveDateFrom;
  final DateTime? effectiveDateTo;

  const BOMFilterCriteria({
    this.productId,
    this.status,
    this.createdBy,
    this.effectiveDateFrom,
    this.effectiveDateTo,
  });
}

/// Purchase order filter criteria
class PurchaseOrderFilterCriteria {
  final String? supplierId;
  final PurchaseOrderStatus? status;
  final DateTime? orderDateFrom;
  final DateTime? orderDateTo;
  final DateTime? deliveryDateFrom;
  final DateTime? deliveryDateTo;
  final String? createdBy;
  final bool? isOverdue;

  const PurchaseOrderFilterCriteria({
    this.supplierId,
    this.status,
    this.orderDateFrom,
    this.orderDateTo,
    this.deliveryDateFrom,
    this.deliveryDateTo,
    this.createdBy,
    this.isOverdue,
  });
}

/// Supplier filter criteria
class SupplierFilterCriteria {
  final SupplierType? type;
  final CommonStatus? status;
  final String? assignedBuyer;
  final List<String>? categories;
  final SupplierRatingCategory? ratingCategory;
  final List<String>? tags;

  const SupplierFilterCriteria({
    this.type,
    this.status,
    this.assignedBuyer,
    this.categories,
    this.ratingCategory,
    this.tags,
  });
}

/// Quotation filter criteria
class QuotationFilterCriteria {
  final String? supplierId;
  final QuotationStatus? status;
  final DateTime? quotationDateFrom;
  final DateTime? quotationDateTo;
  final DateTime? validUntilFrom;
  final DateTime? validUntilTo;
  final String? requestedBy;

  const QuotationFilterCriteria({
    this.supplierId,
    this.status,
    this.quotationDateFrom,
    this.quotationDateTo,
    this.validUntilFrom,
    this.validUntilTo,
    this.requestedBy,
  });
}

// Statistics and reporting classes

/// Inventory statistics
class InventoryStatistics {
  final int totalItems;
  final int activeItems;
  final int lowStockItems;
  final int outOfStockItems;
  final int expiringItems;
  final double totalValue;
  final double averageValue;
  final Map<String, int> itemsByCategory;
  final Map<String, double> valueByCategory;
  final Map<String, int> movementsByType;

  const InventoryStatistics({
    required this.totalItems,
    required this.activeItems,
    required this.lowStockItems,
    required this.outOfStockItems,
    required this.expiringItems,
    required this.totalValue,
    required this.averageValue,
    required this.itemsByCategory,
    required this.valueByCategory,
    required this.movementsByType,
  });
}

/// Inventory valuation
class InventoryValuation {
  final DateTime asOfDate;
  final double totalValue;
  final Map<String, double> valueByCategory;
  final Map<String, double> valueByLocation;
  final List<InventoryValuationItem> items;

  const InventoryValuation({
    required this.asOfDate,
    required this.totalValue,
    required this.valueByCategory,
    required this.valueByLocation,
    required this.items,
  });
}

/// Inventory valuation item
class InventoryValuationItem {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double quantity;
  final double unitCost;
  final double totalValue;

  const InventoryValuationItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.quantity,
    required this.unitCost,
    required this.totalValue,
  });
}

/// Reorder suggestion
class ReorderSuggestion {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double currentStock;
  final double reorderPoint;
  final double suggestedQuantity;
  final String reason;
  final int urgencyLevel;
  final double estimatedCost;

  const ReorderSuggestion({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.currentStock,
    required this.reorderPoint,
    required this.suggestedQuantity,
    required this.reason,
    required this.urgencyLevel,
    required this.estimatedCost,
  });
}

/// Material requirement
class MaterialRequirement {
  final String itemId;
  final String itemCode;
  final String itemName;
  final double requiredQuantity;
  final double availableQuantity;
  final double shortageQuantity;
  final String unit;

  const MaterialRequirement({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.requiredQuantity,
    required this.availableQuantity,
    required this.shortageQuantity,
    required this.unit,
  });
}

/// Material availability report
class MaterialAvailabilityReport {
  final String bomId;
  final double requestedQuantity;
  final bool isAvailable;
  final List<MaterialRequirement> requirements;
  final List<MaterialRequirement> shortages;
  final double totalShortageValue;

  const MaterialAvailabilityReport({
    required this.bomId,
    required this.requestedQuantity,
    required this.isAvailable,
    required this.requirements,
    required this.shortages,
    required this.totalShortageValue,
  });
}

/// Purchase order statistics
class PurchaseOrderStatistics {
  final int totalOrders;
  final int pendingOrders;
  final int receivedOrders;
  final int overdueOrders;
  final double totalValue;
  final double averageOrderValue;
  final Map<String, int> ordersByStatus;
  final Map<String, double> valueBySupplier;
  final double onTimeDeliveryRate;

  const PurchaseOrderStatistics({
    required this.totalOrders,
    required this.pendingOrders,
    required this.receivedOrders,
    required this.overdueOrders,
    required this.totalValue,
    required this.averageOrderValue,
    required this.ordersByStatus,
    required this.valueBySupplier,
    required this.onTimeDeliveryRate,
  });
}

/// Supplier statistics
class SupplierStatistics {
  final int totalOrders;
  final double totalValue;
  final double averageOrderValue;
  final DateTime? lastOrderDate;
  final int onTimeDeliveries;
  final int lateDeliveries;
  final double onTimeDeliveryRate;
  final SupplierRating rating;

  const SupplierStatistics({
    required this.totalOrders,
    required this.totalValue,
    required this.averageOrderValue,
    this.lastOrderDate,
    required this.onTimeDeliveries,
    required this.lateDeliveries,
    required this.onTimeDeliveryRate,
    required this.rating,
  });
}

// Request classes would be defined here for brevity
class CreateInventoryItemRequest {
  final String itemCode;
  final String itemName;
  final String description;
  final ItemCategory category;
  final ItemType type;
  final String unit;
  final double initialStock;
  final double minimumStock;
  final double maximumStock;
  final double reorderPoint;
  final double reorderQuantity;
  final double unitCost;

  const CreateInventoryItemRequest({
    required this.itemCode,
    required this.itemName,
    required this.description,
    required this.category,
    required this.type,
    required this.unit,
    required this.initialStock,
    required this.minimumStock,
    required this.maximumStock,
    required this.reorderPoint,
    required this.reorderQuantity,
    required this.unitCost,
  });
}

class UpdateInventoryItemRequest {
  final String itemId;
  final String? itemName;
  final String? description;
  final double? minimumStock;
  final double? maximumStock;
  final double? reorderPoint;
  final double? reorderQuantity;
  final double? unitCost;

  const UpdateInventoryItemRequest({
    required this.itemId,
    this.itemName,
    this.description,
    this.minimumStock,
    this.maximumStock,
    this.reorderPoint,
    this.reorderQuantity,
    this.unitCost,
  });
}

class AdjustStockRequest {
  final String itemId;
  final double quantity;
  final MovementType movementType;
  final String reason;
  final String? referenceNumber;

  const AdjustStockRequest({
    required this.itemId,
    required this.quantity,
    required this.movementType,
    required this.reason,
    this.referenceNumber,
  });
}

class CreateBOMRequest {
  final String bomCode;
  final String productId;
  final String version;
  final List<BOMItem> items;
  final String? description;

  const CreateBOMRequest({
    required this.bomCode,
    required this.productId,
    required this.version,
    required this.items,
    this.description,
  });
}

class UpdateBOMRequest {
  final String bomId;
  final String? version;
  final List<BOMItem>? items;
  final String? description;

  const UpdateBOMRequest({
    required this.bomId,
    this.version,
    this.items,
    this.description,
  });
}

class CreatePurchaseOrderRequest {
  final String supplierId;
  final DateTime expectedDeliveryDate;
  final List<PurchaseOrderItem> items;
  final String? notes;
  final String? terms;

  const CreatePurchaseOrderRequest({
    required this.supplierId,
    required this.expectedDeliveryDate,
    required this.items,
    this.notes,
    this.terms,
  });
}

class UpdatePurchaseOrderRequest {
  final String poId;
  final DateTime? expectedDeliveryDate;
  final List<PurchaseOrderItem>? items;
  final String? notes;
  final String? terms;

  const UpdatePurchaseOrderRequest({
    required this.poId,
    this.expectedDeliveryDate,
    this.items,
    this.notes,
    this.terms,
  });
}

class ReceivePurchaseOrderRequest {
  final String poId;
  final List<ReceiveItemRequest> items;
  final DateTime receivedDate;
  final String? notes;

  const ReceivePurchaseOrderRequest({
    required this.poId,
    required this.items,
    required this.receivedDate,
    this.notes,
  });
}

class ReceiveItemRequest {
  final String itemId;
  final double receivedQuantity;
  final String? notes;

  const ReceiveItemRequest({
    required this.itemId,
    required this.receivedQuantity,
    this.notes,
  });
}

class CreateSupplierRequest {
  final String supplierCode;
  final String companyName;
  final SupplierType type;
  final SupplierContact primaryContact;
  final SupplierAddress address;
  final SupplierBusinessInfo businessInfo;
  final SupplierTerms terms;

  const CreateSupplierRequest({
    required this.supplierCode,
    required this.companyName,
    required this.type,
    required this.primaryContact,
    required this.address,
    required this.businessInfo,
    required this.terms,
  });
}

class UpdateSupplierRequest {
  final String supplierId;
  final String? companyName;
  final SupplierContact? primaryContact;
  final SupplierAddress? address;
  final SupplierBusinessInfo? businessInfo;
  final SupplierTerms? terms;

  const UpdateSupplierRequest({
    required this.supplierId,
    this.companyName,
    this.primaryContact,
    this.address,
    this.businessInfo,
    this.terms,
  });
}

class CreateQuotationRequest {
  final String supplierId;
  final DateTime validUntil;
  final List<QuotationItem> items;
  final String? notes;
  final String? terms;

  const CreateQuotationRequest({
    required this.supplierId,
    required this.validUntil,
    required this.items,
    this.notes,
    this.terms,
  });
}

// Additional result classes
class InventoryImportResult {
  final int totalRecords;
  final int successfulImports;
  final int failedImports;
  final List<String> errors;

  const InventoryImportResult({
    required this.totalRecords,
    required this.successfulImports,
    required this.failedImports,
    required this.errors,
  });
}

class BulkUpdateResult {
  final int totalRecords;
  final int successfulUpdates;
  final int failedUpdates;
  final List<String> errors;

  const BulkUpdateResult({
    required this.totalRecords,
    required this.successfulUpdates,
    required this.failedUpdates,
    required this.errors,
  });
}

class BulkUpdateInventoryRequest {
  final String itemId;
  final double? minimumStock;
  final double? maximumStock;
  final double? reorderPoint;
  final double? reorderQuantity;
  final double? unitCost;

  const BulkUpdateInventoryRequest({
    required this.itemId,
    this.minimumStock,
    this.maximumStock,
    this.reorderPoint,
    this.reorderQuantity,
    this.unitCost,
  });
}
