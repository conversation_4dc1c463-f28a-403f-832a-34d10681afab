import '../models/base_entity.dart';

/// Experience level for workers
enum ExperienceLevel {
  beginner('Beginner', '0-1 years of experience'),
  intermediate('Intermediate', '1-3 years of experience'),
  experienced('Experienced', '3-5 years of experience'),
  expert('Expert', '5+ years of experience');

  const ExperienceLevel(this.displayName, this.description);
  final String displayName;
  final String description;

  static ExperienceLevel fromString(String value) {
    return ExperienceLevel.values.firstWhere(
      (level) => level.toString().split('.').last == value,
      orElse: () => ExperienceLevel.beginner,
    );
  }
}

/// Status for manufacturing departments
enum DepartmentStatus {
  active('Active', 'Active and operational'),
  maintenance('Maintenance', 'Undergoing maintenance'),
  closed('Closed', 'Temporarily closed'),
  inactive('Inactive', 'Not in use');

  const DepartmentStatus(this.displayName, this.description);
  final String displayName;
  final String description;

  bool get isActive => this == DepartmentStatus.active;
  bool get isInMaintenance => this == DepartmentStatus.maintenance;
  bool get isClosed => this == DepartmentStatus.closed;
  bool get isInactive => this == DepartmentStatus.inactive;

  static DepartmentStatus fromString(String value) {
    return DepartmentStatus.values.firstWhere(
      (status) => status.toString().split('.').last == value,
      orElse: () => DepartmentStatus.inactive,
    );
  }
}

/// Common status enum for various entities
enum CommonStatus {
  active('active'),
  inactive('inactive'),
  pending('pending'),
  suspended('suspended'),
  archived('archived');

  const CommonStatus(this.value);
  final String value;

  bool get isActive => this == CommonStatus.active;
  bool get isInactive => !isActive;

  String get displayName {
    switch (this) {
      case CommonStatus.active:
        return 'Active';
      case CommonStatus.inactive:
        return 'Inactive';
      case CommonStatus.pending:
        return 'Pending';
      case CommonStatus.suspended:
        return 'Suspended';
      case CommonStatus.archived:
        return 'Archived';
    }
  }

  String get colorCode {
    switch (this) {
      case CommonStatus.active:
        return '#10B981'; // Green
      case CommonStatus.inactive:
        return '#6B7280'; // Gray
      case CommonStatus.pending:
        return '#F59E0B'; // Amber
      case CommonStatus.suspended:
        return '#EF4444'; // Red
      case CommonStatus.archived:
        return '#9CA3AF'; // Light Gray
    }
  }

  static CommonStatus fromString(String value) {
    return CommonStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CommonStatus.inactive,
    );
  }
}

/// Priority levels for tasks, orders, etc.
enum Priority {
  low('low'),
  medium('medium'),
  high('high'),
  critical('critical');

  const Priority(this.value);
  final String value;

  int get level {
    switch (this) {
      case Priority.low:
        return 1;
      case Priority.medium:
        return 2;
      case Priority.high:
        return 3;
      case Priority.critical:
        return 4;
    }
  }

  bool get isHigh => level >= 3;
  bool get isLow => level <= 2;

  String get displayName {
    switch (this) {
      case Priority.low:
        return 'Low';
      case Priority.medium:
        return 'Medium';
      case Priority.high:
        return 'High';
      case Priority.critical:
        return 'Critical';
    }
  }

  String get colorCode {
    switch (this) {
      case Priority.low:
        return '#10B981'; // Green
      case Priority.medium:
        return '#F59E0B'; // Amber
      case Priority.high:
        return '#EF4444'; // Red
      case Priority.critical:
        return '#7C2D12'; // Dark Red
    }
  }

  static Priority fromString(String value) {
    return Priority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => Priority.medium,
    );
  }
}

/// Manufacturing departments
enum Department {
  merchandising('merchandising'),
  cutting('cutting'),
  sewing('sewing'),
  quality('quality'),
  finishing('finishing'),
  warehouse('warehouse'),
  administration('administration');

  const Department(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case Department.merchandising:
        return 'Merchandising';
      case Department.cutting:
        return 'Cutting';
      case Department.sewing:
        return 'Sewing';
      case Department.quality:
        return 'Quality Control';
      case Department.finishing:
        return 'Finishing';
      case Department.warehouse:
        return 'Warehouse';
      case Department.administration:
        return 'Administration';
    }
  }

  String get description {
    switch (this) {
      case Department.merchandising:
        return 'Order management and customer relations';
      case Department.cutting:
        return 'Fabric cutting and marker planning';
      case Department.sewing:
        return 'Garment assembly and stitching';
      case Department.quality:
        return 'Quality control and inspection';
      case Department.finishing:
        return 'Final processing and packaging';
      case Department.warehouse:
        return 'Inventory and storage management';
      case Department.administration:
        return 'Administrative and support functions';
    }
  }

  String get colorCode {
    switch (this) {
      case Department.merchandising:
        return '#3B82F6'; // Blue
      case Department.cutting:
        return '#8B5CF6'; // Purple
      case Department.sewing:
        return '#06B6D4'; // Cyan
      case Department.quality:
        return '#EF4444'; // Red
      case Department.finishing:
        return '#10B981'; // Green
      case Department.warehouse:
        return '#F59E0B'; // Amber
      case Department.administration:
        return '#6B7280'; // Gray
    }
  }

  String get iconName {
    switch (this) {
      case Department.merchandising:
        return 'shopping_cart';
      case Department.cutting:
        return 'content_cut';
      case Department.sewing:
        return 'design_services';
      case Department.quality:
        return 'verified';
      case Department.finishing:
        return 'check_circle';
      case Department.warehouse:
        return 'warehouse';
      case Department.administration:
        return 'admin_panel_settings';
    }
  }

  static Department fromString(String value) {
    return Department.values.firstWhere(
      (dept) => dept.value == value,
      orElse: () => Department.administration,
    );
  }
}

/// User roles in the system
enum UserRole {
  administrator('administrator'),
  merchandiser('merchandiser'),
  inventoryManager('inventory_manager'),
  cuttingHead('cutting_head'),
  cuttingMaster('cutting_master'),
  cuttingHelper('cutting_helper'),
  sewingHead('sewing_head'),
  sewingSupervisor('sewing_supervisor'),
  sewingOperator('sewing_operator'),
  qualityController('quality_controller'),
  finishingHead('finishing_head'),
  finishingOperator('finishing_operator'),
  warehouseManager('warehouse_manager'),
  viewer('viewer');

  const UserRole(this.value);
  final String value;

  int get priority {
    switch (this) {
      case UserRole.administrator:
        return 100;
      case UserRole.merchandiser:
        return 90;
      case UserRole.inventoryManager:
        return 80;
      case UserRole.warehouseManager:
        return 70;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return 60;
      case UserRole.cuttingMaster:
      case UserRole.sewingSupervisor:
      case UserRole.qualityController:
        return 50;
      case UserRole.cuttingHelper:
      case UserRole.sewingOperator:
      case UserRole.finishingOperator:
        return 40;
      case UserRole.viewer:
        return 10;
      default:
        return 0;
    }
  }

  String get displayName {
    switch (this) {
      case UserRole.administrator:
        return 'Administrator';
      case UserRole.merchandiser:
        return 'Merchandiser';
      case UserRole.inventoryManager:
        return 'Inventory Manager';
      case UserRole.cuttingHead:
        return 'Cutting Department Head';
      case UserRole.cuttingMaster:
        return 'Cutting Master';
      case UserRole.cuttingHelper:
        return 'Cutting Helper';
      case UserRole.sewingHead:
        return 'Sewing Department Head';
      case UserRole.sewingSupervisor:
        return 'Sewing Line Supervisor';
      case UserRole.sewingOperator:
        return 'Sewing Operator';
      case UserRole.qualityController:
        return 'Quality Controller';
      case UserRole.finishingHead:
        return 'Finishing Department Head';
      case UserRole.finishingOperator:
        return 'Finishing Operator';
      case UserRole.warehouseManager:
        return 'Warehouse Manager';
      case UserRole.viewer:
        return 'Viewer';
    }
  }

  String get description {
    switch (this) {
      case UserRole.administrator:
        return 'Full system access and user management';
      case UserRole.merchandiser:
        return 'Order management and customer relations';
      case UserRole.inventoryManager:
        return 'Inventory and material management';
      case UserRole.cuttingHead:
        return 'Cutting department management';
      case UserRole.cuttingMaster:
        return 'Fabric cutting and marker planning';
      case UserRole.cuttingHelper:
        return 'Cutting assistance and support';
      case UserRole.sewingHead:
        return 'Sewing department management';
      case UserRole.sewingSupervisor:
        return 'Sewing line supervision';
      case UserRole.sewingOperator:
        return 'Garment sewing operations';
      case UserRole.qualityController:
        return 'Quality inspection and control';
      case UserRole.finishingHead:
        return 'Finishing department management';
      case UserRole.finishingOperator:
        return 'Finishing operations';
      case UserRole.warehouseManager:
        return 'Warehouse and storage management';
      case UserRole.viewer:
        return 'Read-only access to reports';
    }
  }

  Department get department {
    switch (this) {
      case UserRole.administrator:
        return Department.administration;
      case UserRole.merchandiser:
        return Department.merchandising;
      case UserRole.inventoryManager:
        return Department.warehouse;
      case UserRole.cuttingHead:
      case UserRole.cuttingMaster:
      case UserRole.cuttingHelper:
        return Department.cutting;
      case UserRole.sewingHead:
      case UserRole.sewingSupervisor:
      case UserRole.sewingOperator:
        return Department.sewing;
      case UserRole.qualityController:
        return Department.quality;
      case UserRole.finishingHead:
      case UserRole.finishingOperator:
        return Department.finishing;
      case UserRole.warehouseManager:
        return Department.warehouse;
      case UserRole.viewer:
        return Department.administration;
    }
  }

  int get hierarchyLevel {
    switch (this) {
      case UserRole.administrator:
        return 10;
      case UserRole.merchandiser:
      case UserRole.inventoryManager:
      case UserRole.warehouseManager:
        return 8;
      case UserRole.cuttingHead:
      case UserRole.sewingHead:
      case UserRole.finishingHead:
        return 7;
      case UserRole.sewingSupervisor:
        return 6;
      case UserRole.cuttingMaster:
      case UserRole.qualityController:
        return 5;
      case UserRole.sewingOperator:
      case UserRole.finishingOperator:
        return 4;
      case UserRole.cuttingHelper:
        return 3;
      case UserRole.viewer:
        return 1;
    }
  }

  List<String> get permissions {
    switch (this) {
      case UserRole.administrator:
        return [
          'users.create',
          'users.read',
          'users.update',
          'users.delete',
          'orders.create',
          'orders.read',
          'orders.update',
          'orders.delete',
          'production.create',
          'production.read',
          'production.update',
          'production.delete',
          'inventory.create',
          'inventory.read',
          'inventory.update',
          'inventory.delete',
          'quality.create',
          'quality.read',
          'quality.update',
          'quality.delete',
          'reports.read',
          'settings.read',
          'settings.update',
        ];
      case UserRole.merchandiser:
        return [
          'orders.create',
          'orders.read',
          'orders.update',
          'production.create',
          'production.read',
          'production.update',
          'customers.create',
          'customers.read',
          'customers.update',
          'reports.read',
        ];
      case UserRole.inventoryManager:
        return [
          'inventory.create',
          'inventory.read',
          'inventory.update',
          'inventory.delete',
          'materials.create',
          'materials.read',
          'materials.update',
          'materials.delete',
          'reports.read',
        ];
      case UserRole.cuttingHead:
        return [
          'cutting.read',
          'cutting.update',
          'tasks.create',
          'tasks.read',
          'tasks.update',
          'tasks.assign',
          'production.read',
          'production.update',
          'reports.read',
        ];
      case UserRole.cuttingMaster:
        return [
          'cutting.read',
          'cutting.update',
          'tasks.read',
          'tasks.update',
          'production.read',
        ];
      case UserRole.cuttingHelper:
        return [
          'cutting.read',
          'tasks.read',
          'tasks.update',
        ];
      case UserRole.sewingHead:
        return [
          'sewing.read',
          'sewing.update',
          'tasks.create',
          'tasks.read',
          'tasks.update',
          'tasks.assign',
          'production.read',
          'production.update',
          'reports.read',
        ];
      case UserRole.sewingSupervisor:
        return [
          'sewing.read',
          'sewing.update',
          'tasks.read',
          'tasks.update',
          'tasks.assign',
          'production.read',
          'production.update',
        ];
      case UserRole.sewingOperator:
        return [
          'sewing.read',
          'tasks.read',
          'tasks.update',
        ];
      case UserRole.qualityController:
        return [
          'quality.create',
          'quality.read',
          'quality.update',
          'inspections.create',
          'inspections.read',
          'inspections.update',
          'production.read',
          'reports.read',
        ];
      case UserRole.finishingHead:
        return [
          'finishing.read',
          'finishing.update',
          'tasks.create',
          'tasks.read',
          'tasks.update',
          'tasks.assign',
          'production.read',
          'production.update',
          'reports.read',
        ];
      case UserRole.finishingOperator:
        return [
          'finishing.read',
          'tasks.read',
          'tasks.update',
        ];
      case UserRole.warehouseManager:
        return [
          'warehouse.read',
          'warehouse.update',
          'inventory.read',
          'inventory.update',
          'shipping.create',
          'shipping.read',
          'shipping.update',
          'reports.read',
        ];
      case UserRole.viewer:
        return [
          'reports.read',
          'dashboard.read',
        ];
    }
  }

  bool hasPermission(String permission) => permissions.contains(permission);

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.viewer,
    );
  }
}

/// Order status enum
enum OrderStatus {
  draft('draft'),
  pending('pending'),
  confirmed('confirmed'),
  inProduction('in_production'),
  qualityCheck('quality_check'),
  completed('completed'),
  shipped('shipped'),
  delivered('delivered'),
  cancelled('cancelled'),
  onHold('on_hold');

  const OrderStatus(this.value);
  final String value;

  bool get isActive {
    switch (this) {
      case OrderStatus.draft:
      case OrderStatus.pending:
      case OrderStatus.confirmed:
      case OrderStatus.inProduction:
        return true;
        case OrderStatus.qualityCheck:
        return false;
      case OrderStatus.completed:
      case OrderStatus.shipped:
      case OrderStatus.delivered:
      case OrderStatus.cancelled:
      case OrderStatus.onHold:
        return false;
      case OrderStatus.qualityCheck:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  String get displayName {
    switch (this) {
      case OrderStatus.draft:
        return 'Draft';
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProduction:
        return 'In Production';
        case OrderStatus.qualityCheck:
        return 'Quality Check';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.onHold:
        return 'On Hold';
      case OrderStatus.qualityCheck:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  String get colorCode {
    switch (this) {
      case OrderStatus.draft:
        return '#6B7280'; // Gray
      case OrderStatus.pending:
        return '#F59E0B'; // Amber
      case OrderStatus.confirmed:
        return '#3B82F6'; // Blue
      case OrderStatus.inProduction:
        return '#8B5CF6'; // Purple
      case OrderStatus.qualityCheck:
        return '#EF4444'; // Red
      case OrderStatus.completed:
        return '#10B981'; // Green
      case OrderStatus.shipped:
        return '#06B6D4'; // Cyan
      case OrderStatus.delivered:
        return '#059669'; // Dark Green
      case OrderStatus.cancelled:
        return '#EF4444'; // Red
      case OrderStatus.onHold:
        return '#F97316'; // Orange
    }
  }

  static OrderStatus fromString(String value) {
    return OrderStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => OrderStatus.draft,
    );
  }
}

/// Resource allocation status
enum AllocationStatus {
  allocated('allocated'),
  inUse('in_use'),
  completed('completed'),
  released('released'),
  cancelled('cancelled');

  const AllocationStatus(this.value);
  final String value;

  /// Returns the display name of the status
  String get displayName {
    switch (this) {
      case AllocationStatus.allocated:
        return 'Allocated';
      case AllocationStatus.inUse:
        return 'In Use';
      case AllocationStatus.completed:
        return 'Completed';
      case AllocationStatus.released:
        return 'Released';
      case AllocationStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Returns true if the status indicates an active allocation
  bool get isActive => this == AllocationStatus.allocated || this == AllocationStatus.inUse;

  /// Returns true if the status indicates a completed allocation
  bool get isCompleted => this == AllocationStatus.completed || this == AllocationStatus.released || this == AllocationStatus.cancelled;

  /// Creates an AllocationStatus from a string value
  static AllocationStatus fromString(String value) {
    return AllocationStatus.values.firstWhere(
      (status) => status.value == value || status.toString().split('.').last == value,
      orElse: () => AllocationStatus.allocated,
    );
  }
}

/// Task status enum
enum TaskStatus {
  pending('pending'),
  ready('ready'),
  notStarted('not_started'),
  assigned('assigned'),
  inProgress('in_progress'),
  paused('paused'),
  completed('completed'),
  cancelled('cancelled'),
  blocked('blocked'),
  onHold('on_hold');

  const TaskStatus(this.value);
  final String value;

  bool get isActive {
    switch (this) {
      case TaskStatus.pending:
      case TaskStatus.ready:
      case TaskStatus.notStarted:
      case TaskStatus.inProgress:
        return true;
      case TaskStatus.paused:
      case TaskStatus.completed:
      case TaskStatus.cancelled:
      case TaskStatus.blocked:
      case TaskStatus.onHold:
        return false;
      case TaskStatus.assigned:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  String get displayName {
    switch (this) {
      case TaskStatus.pending:
        return 'Pending';
      case TaskStatus.ready:
        return 'Ready';
      case TaskStatus.notStarted:
        return 'Not Started';
      case TaskStatus.assigned:
        return 'Assigned';
      case TaskStatus.inProgress:
        return 'In Progress';
      case TaskStatus.paused:
        return 'Paused';
      case TaskStatus.completed:
        return 'Completed';
      case TaskStatus.cancelled:
        return 'Cancelled';
      case TaskStatus.blocked:
        return 'Blocked';
      case TaskStatus.onHold:
        return 'On Hold';
    }
  }

  String get colorCode {
    switch (this) {
      case TaskStatus.pending:
      case TaskStatus.notStarted:
        return '#6B7280'; // Gray
      case TaskStatus.ready:
      case TaskStatus.assigned: // Assigned tasks have the same color as ready tasks
        return '#8B5CF6'; // Purple
      case TaskStatus.inProgress:
        return '#3B82F6'; // Blue
      case TaskStatus.paused:
        return '#F59E0B'; // Amber
      case TaskStatus.completed:
        return '#10B981'; // Green
      case TaskStatus.cancelled:
        return '#EF4444'; // Red
      case TaskStatus.blocked:
      case TaskStatus.onHold:
        return '#F97316'; // Orange
    }
  }

  static TaskStatus fromString(String value) {
    return TaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaskStatus.pending,
    );
  }

  // Helper getters for backward compatibility
  bool get isCompleted => this == TaskStatus.completed;
  bool get canStart => this == TaskStatus.ready;
}



/// Order priority enum
enum OrderPriority {
  low('low'),
  normal('normal'),
  high('high'),
  urgent('urgent'),
  critical('critical');

  const OrderPriority(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case OrderPriority.low:
        return 'Low';
      case OrderPriority.normal:
        return 'Normal';
      case OrderPriority.high:
        return 'High';
      case OrderPriority.urgent:
        return 'Urgent';
      case OrderPriority.critical:
        return 'Critical';
    }
  }

  String get colorCode {
    switch (this) {
      case OrderPriority.low:
        return '#10B981'; // Green
      case OrderPriority.normal:
        return '#6B7280'; // Gray
      case OrderPriority.high:
        return '#F59E0B'; // Amber
      case OrderPriority.urgent:
        return '#EF4444'; // Red
      case OrderPriority.critical:
        return '#7C2D12'; // Dark Red
    }
  }

  int get level {
    switch (this) {
      case OrderPriority.low:
        return 1;
      case OrderPriority.normal:
        return 2;
      case OrderPriority.high:
        return 3;
      case OrderPriority.urgent:
        return 4;
      case OrderPriority.critical:
        return 5;
    }
  }

  static OrderPriority fromString(String value) {
    return OrderPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => OrderPriority.normal,
    );
  }
}

/// Order type enum
enum OrderType {
  regular('regular'),
  sample('sample'),
  bulk('bulk'),
  repeat('repeat'),
  urgent('urgent'),
  export('export'),
  domestic('domestic');

  const OrderType(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case OrderType.regular:
        return 'Regular';
      case OrderType.sample:
        return 'Sample';
      case OrderType.bulk:
        return 'Bulk';
      case OrderType.repeat:
        return 'Repeat';
      case OrderType.urgent:
        return 'Urgent';
      case OrderType.export:
        return 'Export';
      case OrderType.domestic:
        return 'Domestic';
    }
  }

  String get colorCode {
    switch (this) {
      case OrderType.regular:
        return '#6B7280'; // Gray
      case OrderType.sample:
        return '#3B82F6'; // Blue
      case OrderType.bulk:
        return '#10B981'; // Green
      case OrderType.repeat:
        return '#8B5CF6'; // Purple
      case OrderType.urgent:
        return '#EF4444'; // Red
      case OrderType.export:
        return '#F59E0B'; // Amber
      case OrderType.domestic:
        return '#06B6D4'; // Cyan
    }
  }

  static OrderType fromString(String value) {
    return OrderType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => OrderType.regular,
    );
  }
}

/// Work shifts for manufacturing employees
enum WorkShift {
  day('Day', '8:00 AM - 5:00 PM'),
  night('Night', '6:00 PM - 3:00 AM');

  const WorkShift(this.displayName, this.description);
  final String displayName;
  final String description;

  static WorkShift fromString(String value) {
    return WorkShift.values.firstWhere(
      (shift) => shift.toString().split('.').last == value,
      orElse: () => WorkShift.day,
    );
  }
}
