import 'package:equatable/equatable.dart';

import '../../../shared/enums/common_enums.dart';
import '../../../shared/models/base_entity.dart';

/// User entity
class AppUser extends BaseEntity {
  final String uid;
  final String email;
  final String? displayName;
  final String? photoURL;
  final bool emailVerified;
  final bool isAnonymous;
  final String? phoneNumber;
  final UserRole role;
  final CommonStatus status;
  final UserProfile profile;
  final List<String> permissions;
  final List<String> departmentIds;
  final String? currentDepartmentId;
  final DateTime? lastLoginAt;
  final DateTime? lastActiveAt;
  final Map<String, dynamic> metadata;

  const AppUser({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    required this.uid,
    required this.email,
    this.displayName,
    this.photoURL,
    this.emailVerified = false,
    this.isAnonymous = false,
    this.phoneNumber,
    required this.role,
    required this.status,
    required this.profile,
    this.permissions = const [],
    this.departmentIds = const [],
    this.currentDepartmentId,
    this.lastLoginAt,
    this.lastActiveAt,
    this.metadata = const {},
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          deletedAt: deletedAt,
        );

  @override
  List<Object?> get props => [
        ...super.props,
        uid,
        email,
        displayName,
        photoURL,
        emailVerified,
        phoneNumber,
        role,
        status,
        profile,
        permissions,
        departmentIds,
        currentDepartmentId,
        lastLoginAt,
        lastActiveAt,
        metadata,
      ];

  /// Check if user is active
  bool get isActive => status == CommonStatus.active;

  /// Check if user is admin
  bool get isAdmin => role == UserRole.administrator;

  /// Check if user is manager
  bool get isManager => role == UserRole.merchandiser;

  /// Check if user is supervisor
  bool get isSupervisor => role == UserRole.sewingSupervisor;

  /// Check if user is operator
  bool get isOperator => role == UserRole.sewingOperator;

  /// Check if user has permission
  bool hasPermission(String permission) {
    return permissions.contains(permission) || isAdmin;
  }

  /// Check if user can access department
  bool canAccessDepartment(String departmentId) {
    return departmentIds.contains(departmentId) || isAdmin;
  }

  /// Get user's full name
  String get fullName => profile.fullName;

  /// Get user's initials
  String get initials {
    final names = fullName.split(' ');
    if (names.length >= 2) {
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names.first[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  /// Copy with new values
  AppUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool? emailVerified,
    String? phoneNumber,
    UserRole? role,
    CommonStatus? status,
    UserProfile? profile,
    List<String>? permissions,
    List<String>? departmentIds,
    String? currentDepartmentId,
    DateTime? lastLoginAt,
    DateTime? lastActiveAt,
    Map<String, dynamic>? metadata,
  }) {
    return AppUser(
      id: id,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      deletedAt: deletedAt,
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      status: status ?? this.status,
      profile: profile ?? this.profile,
      permissions: permissions ?? this.permissions,
      departmentIds: departmentIds ?? this.departmentIds,
      currentDepartmentId: currentDepartmentId ?? this.currentDepartmentId,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      metadata: metadata ?? this.metadata,
    );
  }

  factory AppUser.fromFirestore(dynamic doc) {
    final data = doc.data() as Map<String, dynamic>;

    return AppUser(
      id: doc.id,
      createdAt: (data['createdAt'] as dynamic)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as dynamic)?.toDate() ?? DateTime.now(),
      uid: data['uid'] ?? '',
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      emailVerified: data['emailVerified'] ?? false,
      phoneNumber: data['phoneNumber'],
      role: UserRole.values.firstWhere(
        (role) => role.name == data['role'],
        orElse: () => UserRole.sewingOperator,
      ),
      status: CommonStatus.values.firstWhere(
        (status) => status.name == data['status'],
        orElse: () => CommonStatus.pending,
      ),
      profile: UserProfile.fromMap(data['profile'] ?? {}),
      permissions: List<String>.from(data['permissions'] ?? []),
      departmentIds: List<String>.from(data['departmentIds'] ?? []),
      currentDepartmentId: data['currentDepartmentId'],
      lastLoginAt: (data['lastLoginAt'] as dynamic)?.toDate(),
      lastActiveAt: (data['lastActiveAt'] as dynamic)?.toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }
}

/// User profile entity
class UserProfile extends Equatable {
  final String firstName;
  final String lastName;
  final String? middleName;
  final String? employeeId;
  final String? jobTitle;
  final String? department;
  final String? phoneNumber;
  final String? address;
  final DateTime? dateOfBirth;
  final DateTime? hireDate;
  final String? emergencyContact;
  final String? emergencyPhone;
  final Map<String, dynamic> customFields;
  final String? profileImageUrl;

  // Additional properties for compatibility
  String get position => jobTitle ?? '';
  EmergencyContact get emergencyContactInfo => EmergencyContact(
    name: emergencyContact ?? '',
    phone: emergencyPhone ?? '',
    relationship: '',
  );

  factory UserProfile({
    String? firstName,
    String? lastName,
    String? middleName,
    String? employeeId,
    String? jobTitle,
    String? department,
    String? phoneNumber,
    String? address,
    DateTime? dateOfBirth,
    DateTime? hireDate,
    String? emergencyContact,
    String? emergencyPhone,
    String? profileImageUrl,
    Map<String, dynamic> customFields = const {},
  }) {
    if (profileImageUrl != null && Uri.tryParse(profileImageUrl)?.isAbsolute != true) {
      throw ArgumentError('profileImageUrl must be a valid URL');
    }
    
    return UserProfile._(
      firstName: firstName ?? '',
      lastName: lastName ?? '',
      middleName: middleName,
      employeeId: employeeId,
      jobTitle: jobTitle,
      department: department,
      phoneNumber: phoneNumber,
      address: address,
      dateOfBirth: dateOfBirth,
      hireDate: hireDate,
      emergencyContact: emergencyContact,
      emergencyPhone: emergencyPhone,
      profileImageUrl: profileImageUrl,
      customFields: customFields,
    );
  }

  const UserProfile._({
    this.firstName = '', // Nullable field
    this.lastName = '',
    this.middleName,
    this.employeeId,
    this.jobTitle,
    this.department,
    this.phoneNumber,
    this.address,
    this.dateOfBirth,
    this.hireDate,
    this.emergencyContact,
    this.emergencyPhone,
    this.profileImageUrl,
    this.customFields = const {},
  });

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        middleName,
        employeeId,
        jobTitle,
        department,
        phoneNumber,
        address,
        dateOfBirth,
        hireDate,
        emergencyContact,
        emergencyPhone,
        customFields,
        profileImageUrl,
      ];

  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      middleName: map['middleName'],
      employeeId: map['employeeId'],
      jobTitle: map['jobTitle'],
      department: map['department'],
      phoneNumber: map['phoneNumber'],
      address: map['address'],
      dateOfBirth: map['dateOfBirth'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dateOfBirth'])
          : null,
      hireDate: map['hireDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['hireDate'])
          : null,
      emergencyContact: map['emergencyContact'],
      emergencyPhone: map['emergencyPhone'],
      customFields: Map<String, dynamic>.from(map['customFields'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'middleName': middleName,
      'employeeId': employeeId,
      'jobTitle': jobTitle,
      'department': department,
      'phoneNumber': phoneNumber,
      'address': address,
      'dateOfBirth': dateOfBirth?.millisecondsSinceEpoch,
      'hireDate': hireDate?.millisecondsSinceEpoch,
      'emergencyContact': emergencyContact,
      'emergencyPhone': emergencyPhone,
      'customFields': customFields,
    };
  }

  /// Get full name
  String get fullName {
    final parts = [firstName, middleName, lastName].where((part) => part != null && part.isNotEmpty);
    return parts.join(' ');
  }

  /// Copy with new values
  UserProfile copyWith({
    String? firstName,
    String? lastName,
    String? middleName,
    String? employeeId,
    String? jobTitle,
    String? department,
    String? phoneNumber,
    String? address,
    DateTime? dateOfBirth,
    DateTime? hireDate,
    String? emergencyContact,
    String? emergencyPhone,
    String? profileImageUrl,
    Map<String, dynamic>? customFields,
  }) {
    return UserProfile(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      employeeId: employeeId ?? this.employeeId,
      jobTitle: jobTitle ?? this.jobTitle,
      department: department ?? this.department,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      hireDate: hireDate ?? this.hireDate,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      customFields: customFields ?? this.customFields,
    );
  }
}

/// User session entity
class UserSession extends Equatable {
  final String sessionId;
  final String userId;
  final String deviceId;
  final String deviceName;
  final String platform;
  final String appVersion;
  final DateTime loginAt;
  final DateTime? logoutAt;
  final DateTime lastActiveAt;
  final String ipAddress;
  final Map<String, String>? location;
  final bool isActive;

  const UserSession({
    required this.sessionId,
    required this.userId,
    required this.deviceId,
    required this.deviceName,
    required this.platform,
    required this.appVersion,
    required this.loginAt,
    this.logoutAt,
    required this.lastActiveAt,
    required this.ipAddress,
    this.location,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        sessionId,
        userId,
        deviceId,
        deviceName,
        platform,
        appVersion,
        loginAt,
        logoutAt,
        lastActiveAt,
        ipAddress,
        location,
        isActive,
      ];

  factory UserSession.fromFirestore(dynamic doc) {
    final data = doc.data() as Map<String, dynamic>;

    return UserSession(
      sessionId: data['sessionId'] ?? '',
      userId: data['userId'] ?? '',
      deviceId: data['deviceId'] ?? '',
      deviceName: data['deviceName'] ?? '',
      platform: data['platform'] ?? '',
      appVersion: data['appVersion'] ?? '',
      loginAt: (data['loginAt'] as dynamic)?.toDate() ?? DateTime.now(),
      logoutAt: (data['logoutAt'] as dynamic)?.toDate(),
      lastActiveAt: (data['lastActiveAt'] as dynamic)?.toDate() ?? DateTime.now(),
      ipAddress: data['ipAddress'] ?? '',
      location: data['location'] != null
          ? Map<String, String>.from(data['location'])
          : null,
      isActive: data['isActive'] ?? false,
    );
  }

  /// Get session duration
  Duration get sessionDuration {
    final endTime = logoutAt ?? DateTime.now();
    return endTime.difference(loginAt);
  }

  /// Check if session is expired
  bool get isExpired {
    if (!isActive) return true;
    final now = DateTime.now();
    final maxInactivity = const Duration(hours: 24);
    return now.difference(lastActiveAt) > maxInactivity;
  }
}

// Note: UserRole and UserStatus enums are defined in shared/enums/common_enums.dart

/// Emergency contact entity
class EmergencyContact extends Equatable {
  final String name;
  final String phone;
  final String relationship;

  const EmergencyContact({
    required this.name,
    required this.phone,
    required this.relationship,
  });

  @override
  List<Object> get props => [name, phone, relationship];

  factory EmergencyContact.fromMap(Map<String, dynamic> map) {
    return EmergencyContact(
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      relationship: map['relationship'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phone': phone,
      'relationship': relationship,
    };
  }
}
