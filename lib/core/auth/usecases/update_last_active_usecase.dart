import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../shared/models/api_response.dart';
import '../../../features/auth/domain/repositories/auth_repository.dart';
import '../../errors/failures.dart';
import '../../usecases/usecase.dart';

/// Update last active timestamp use case
@injectable
class UpdateLastActiveUseCase implements UseCase<ApiVoidResponse, NoParams> {
  final AuthRepository _repository;

  const UpdateLastActiveUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(NoParams params) async {
    // Implementation will be added once we know the repository method
    // This is a placeholder that returns success for now
    return const Right(ApiVoidResponse(success: true));
  }
}
