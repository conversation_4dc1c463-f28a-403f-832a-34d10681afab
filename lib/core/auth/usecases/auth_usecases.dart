import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import 'package:hm_collection/features/auth/domain/repositories/auth_repository.dart';
import 'package:hm_collection/features/auth/domain/entities/auth_tokens.dart';
import 'package:hm_collection/shared/models/api_response.dart';
import '../../../features/auth/domain/entities/login_credentials.dart' show LoginCredentials;
import '../../../features/auth/domain/repositories/auth_repository.dart' as feature_auth;
import '../../errors/failures.dart';
import '../../usecases/usecase.dart';
import 'package:hm_collection/features/auth/domain/entities/user.dart';

import '../entities/user_entities.dart';
import 'package:hm_collection/core/auth/repositories/auth_repository.dart' as core_auth;

/// Update user profile use case
@injectable
class UpdateUserProfileUseCase implements UseCase<ApiResponse<User>, User> {
  final feature_auth.AuthRepository _repository;

  const UpdateUserProfileUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(User user) async {
    return await _repository.updateProfile(user);
  }
}

/// Login use case
@injectable
class LoginUseCase implements UseCase<ApiResponse<AuthTokens>, LoginCredentials> {
  final AuthRepository _repository;

  const LoginUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> call(
    LoginCredentials credentials,
  ) async {
    return await _repository.login(credentials);
  }
}

/// Admin login use case
@injectable
class AdminLoginUseCase implements UseCase<ApiResponse<AuthTokens>, LoginCredentials> {
  final AuthRepository _repository;

  const AdminLoginUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<AuthTokens>>> call(
    LoginCredentials credentials,
  ) async {
    return await _repository.adminLogin(credentials);
  }
}

/// Register use case
@injectable
class RegisterUseCase implements UseCase<ApiResponse, RegisterRequest> {
  final AuthRepository _repository;

  const RegisterUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse>> call(RegisterRequest request) async {
    return await _repository.register(request);
  }
}

/// Logout use case
@injectable
class LogoutUseCase implements UseCase<ApiVoidResponse, NoParams> {
  final AuthRepository _repository;

  const LogoutUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(NoParams params) async {
    return await _repository.logout();
  }
}

/// Get current user use case
@injectable
class GetCurrentUserUseCase implements UseCase<ApiResponse<User>, NoParams> {
  final AuthRepository _repository;

  const GetCurrentUserUseCase(this._repository);

  @override
  Future<Either<Failure, ApiResponse<User>>> call(NoParams params) async {
    return await _repository.getCurrentUser();
  }
}

/// Change password use case
@injectable
class ChangePasswordUseCase
    implements UseCase<ApiVoidResponse, ChangePasswordRequest> {
  final AuthRepository _repository;

  const ChangePasswordUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(
      ChangePasswordRequest request) async {
    return await _repository.changePassword(request);
  }
}

/// Request password reset use case
@injectable
class RequestPasswordResetUseCase
    implements UseCase<ApiVoidResponse, PasswordResetRequest> {
  final AuthRepository _repository;

  const RequestPasswordResetUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(
      PasswordResetRequest request) async {
    return await _repository.requestPasswordReset(request);
  }
}

/// Check email verified use case
@injectable
class IsEmailVerifiedUseCase implements UseCase<bool, NoParams> {
  final AuthRepository _repository;

  const IsEmailVerifiedUseCase(this._repository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return await _repository.isEmailVerified();
  }
}

/// Send email verification use case
@injectable
class SendEmailVerificationUseCase implements UseCase<void, NoParams> {
  final AuthRepository _repository;

  const SendEmailVerificationUseCase(this._repository);

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    return await _repository.sendEmailVerification();
  }
}

/// Refresh user data use case
@injectable
class RefreshUserDataUseCase implements UseCase<AppUser, NoParams> {
  final core_auth.AuthRepository _repository;

  const RefreshUserDataUseCase(this._repository);

  @override
  Future<Either<Failure, AppUser>> call(NoParams params) async {
    return await _repository.refreshUserData();
  }
}

/// Send password reset email use case
@injectable
class SendPasswordResetEmailUseCase implements UseCase<ApiVoidResponse, String> {
  final AuthRepository _repository;

  const SendPasswordResetEmailUseCase(this._repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(String email) async {
    // Use the domain repository's requestPasswordReset with a simple email param
    return await _repository.requestPasswordReset(PasswordResetRequest(email: email));
  }
}

/// Update password use case
@injectable
class UpdatePasswordUseCase {
  final core_auth.AuthRepository _repository;

  const UpdatePasswordUseCase(this._repository);

  /// Executes the use case to update the user's password
  /// 
  /// [params] A record containing:
  /// - currentPassword: The user's current password
  /// - newPassword: The new password to set
  Future<Either<Failure, void>> call({
    required String currentPassword,
    required String newPassword,
  }) async {
    return await _repository.updatePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
  }
}