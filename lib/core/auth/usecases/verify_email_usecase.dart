import 'package:dartz/dartz.dart';
import 'package:hm_collection/core/auth/repositories/auth_repository.dart';
import 'package:hm_collection/core/errors/failures.dart';
import 'package:injectable/injectable.dart';

/// Verify email use case
@injectable
class VerifyEmailUseCase {
  final AuthRepository _repository;

  const VerifyEmailUseCase(this._repository);

  /// Executes the use case to verify the user's email
  Future<Either<Failure, void>> call() async {
    return await _repository.verifyEmail();
  }
}
