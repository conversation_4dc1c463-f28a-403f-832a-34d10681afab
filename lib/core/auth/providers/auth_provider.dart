import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hm_collection/core/firebase/firebase_auth_service.dart';
import 'package:hm_collection/features/auth/domain/entities/user.dart' as app_user;

enum AuthState { loading, authenticated, unauthenticated }

class AuthProvider with ChangeNotifier {
  final FirebaseAuthService _authService;
  late StreamSubscription<app_user.User?> _userStreamSubscription;

  AuthState _authState = AuthState.loading;
  AuthState get authState => _authState;

  app_user.User? _user;
  app_user.User? get user => _user;
  
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  AuthProvider(this._authService) {
    _userStreamSubscription = _authService.userStream.listen(_onUserChanged);
    _checkInitialState();
  }

  void _checkInitialState() {
    if (!_authService.isSignedIn) {
      _authState = AuthState.unauthenticated;
      notifyListeners();
    }
  }

  void _onUserChanged(app_user.User? user) {
    _user = user;
    if (user != null) {
      _authState = AuthState.authenticated;
      _errorMessage = null; // Clear any error on successful auth
    } else {
      _authState = AuthState.unauthenticated;
    }
    notifyListeners();
  }
  
  /// Sets an error message and notifies listeners
  void setError(String? message) {
    _errorMessage = message;
    if (message != null) {
      _authState = AuthState.unauthenticated;
    }
    notifyListeners();
  }
  
  /// Checks if the current user has the specified permission
  /// Returns true if the user has the permission, false otherwise
  bool hasPermission(String permission) {
    if (_user == null) return false;
    // Assuming the User class has a permissions list or similar
    // Adjust this logic based on how permissions are stored in your User model
    return _user!.permissions?.contains(permission) ?? false;
  }
  
  /// Updates the user profile and notifies listeners
  void updateProfile(app_user.User user) {
    _user = user;
    _authState = AuthState.authenticated;
    _errorMessage = null; // Clear any error on successful update
    notifyListeners();
  }
  
  /// Clears any existing error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _userStreamSubscription.cancel();
    super.dispose();
  }
}
