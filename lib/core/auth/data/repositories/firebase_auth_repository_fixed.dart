import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../errors/error_handler.dart';
import '../../../errors/failures.dart';
import '../../entities/user_entities.dart';
import '../../repositories/auth_repository.dart';
import '../../../../shared/enums/common_enums.dart';

/// Fixed Firebase authentication repository implementation
@LazySingleton(as: AuthRepository)
class FirebaseAuthRepositoryFixed implements AuthRepository {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  FirebaseAuthRepositoryFixed(this._firebaseAuth, this._firestore);

  @override
  Future<Either<Failure, bool>> hasAnyAdmin() async {
    try {
      final querySnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'administrator')
          .limit(1)
          .get();
      
      return Right(querySnapshot.docs.isNotEmpty);
    } on FirebaseException catch (e) {
      return Left(ServerFailure(
        'Failed to check for admin users',
        code: e.code,
        details: e.stackTrace,
      ));
    } catch (e, stackTrace) {
      return Left(ServerFailure(
        'An unexpected error occurred',
        details: stackTrace,
      ));
    }
  }

  @override
  Stream<AppUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().asyncMap((user) async {
      if (user == null) return null;
      try {
        final userData = await _getUserDataFromFirestore(user.uid);
        return userData;
      } catch (e) {
        debugPrint('Error in authStateChanges: $e');
        return null;
      }
    });
  }

  @override
  Stream<AppUser?> get userDataChanges {
    return _firebaseAuth.authStateChanges().asyncExpand((user) {
      if (user == null) return Stream.value(null);
      return _firestore
          .collection('users')
          .doc(user.uid)
          .snapshots()
          .map((doc) {
            try {
              return doc.exists ? _parseUserFromFirestore(doc) : null;
            } catch (e) {
              debugPrint('Error parsing user from Firestore: $e');
              return null;
            }
          });
    });
  }

  @override
  Future<Either<Failure, AppUser?>> getCurrentUser() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return const Right(null);

      final userData = await _getUserDataFromFirestore(user.uid);
      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(AuthFailure('Sign in failed'));
      }

      final userData = await _getUserDataFromFirestore(credential.user!.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      // Check if user is active
      if (userData.status != CommonStatus.active) {
        return Left(AuthFailure(
            'Account is ${userData.status.displayName.toLowerCase()}. Please contact administrator for approval.'));
      }

      // Validate user role and permissions
      if (!_isValidUserRole(userData.role)) {
        await _firebaseAuth.signOut();
        return const Left(
            AuthFailure('Invalid user role. Please contact administrator.'));
      }

      // Check role-based login restrictions
      final loginValidation =
          _validateRoleForLogin(userData.role, userData.status);
      if (loginValidation.isLeft()) {
        await _firebaseAuth.signOut();
        return loginValidation.fold(
          (failure) => Left(failure),
          (_) => const Left(AuthFailure('Login validation failed')),
        );
      }

      // Update last login timestamp
      await _updateLastLogin(userData.uid);

      return Right(userData);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    UserRole role = UserRole.sewingOperator,
  }) async {
    try {
      debugPrint('Starting sign up process for email: $email with role: ${role.displayName}');

      // Validate role selection for public signup
      final roleValidation = _validateRoleSelection(role, isPublicSignup: true);
      if (roleValidation.isLeft()) {
        return roleValidation.fold(
          (failure) => Left(failure),
          (_) => const Left(AuthFailure('Role validation failed')),
        );
      }

      // Check if role requires admin approval
      if (_requiresAdminApproval(role)) {
        debugPrint('Role ${role.displayName} requires admin approval');
      }

      // Check if user already exists
      try {
        final signInMethods = await _firebaseAuth.fetchSignInMethodsForEmail(email);
        if (signInMethods.isNotEmpty) {
          return const Left(AuthFailure(
            'An account with this email already exists. Please sign in instead.',
          ));
        }
      } catch (e) {
        debugPrint('Error checking existing user: $e');
      }

      // Use safe user creation method to avoid PigeonUserDetails errors
      return await _createUserSafely(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        role: role,
      );
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during sign up: ${e.code} - ${e.message}');
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      debugPrint('Unexpected error during sign up: $e');
      
      // Handle PigeonUserDetails error specifically
      if (e.toString().contains('PigeonUserDetails')) {
        debugPrint('PigeonUserDetails error detected in main catch block');
        return Left(AuthFailure(_getPigeonUserDetailsErrorMessage()));
      }

      return Left(ErrorHandler.handleException(e));
    }
  }

  /// Safe user creation method that handles PigeonUserDetails errors
  Future<Either<Failure, AppUser>> _createUserSafely({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required UserRole role,
  }) async {
    UserCredential? credential;
    
    try {
      // Attempt 1: Standard Firebase user creation
      debugPrint('Attempting standard Firebase user creation');
      credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user == null) {
        return const Left(AuthFailure('User creation failed'));
      }

      debugPrint('Firebase user created successfully: ${credential.user!.uid}');
      
      // Create user document with safe serialization
      return await _createUserDocumentSafely(
        user: credential.user!,
        email: email,
        firstName: firstName,
        lastName: lastName,
        role: role,
      );
      
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException: ${e.code} - ${e.message}');
      
      // Handle specific Firebase Auth errors
      if (e.code == 'email-already-in-use') {
        return const Left(AuthFailure(
          'This email is already registered. Please sign in instead.',
        ));
      } else if (e.code == 'weak-password') {
        return const Left(AuthFailure(
          'The password is too weak. Please choose a stronger password.',
        ));
      } else if (e.code == 'invalid-email') {
        return const Left(AuthFailure(
          'The email address is invalid. Please enter a valid email.',
        ));
      }
      
      // Check for PigeonUserDetails error in Firebase Auth exception
      if (e.toString().contains('PigeonUserDetails')) {
        debugPrint('PigeonUserDetails error in Firebase Auth - attempting fallback');
        return await _createUserWithAlternativeMethod(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          role: role,
        );
      }
      
      return Left(AuthFailure(e.message ?? 'Authentication failed'));
      
    } catch (e) {
      debugPrint('Unexpected error in user creation: $e');
      
      // Clean up Firebase user if it was created but Firestore failed
      if (credential?.user != null) {
        try {
          await credential!.user!.delete();
          debugPrint('Cleaned up Firebase user after error');
        } catch (cleanupError) {
          debugPrint('Failed to clean up Firebase user: $cleanupError');
        }
      }
      
      // Handle PigeonUserDetails error
      if (e.toString().contains('PigeonUserDetails')) {
        debugPrint('PigeonUserDetails error detected - attempting alternative method');
        return await _createUserWithAlternativeMethod(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
          role: role,
        );
      }
      
      return Left(AuthFailure('User creation failed: ${e.toString()}'));
    }
  }

  /// Alternative user creation method for PigeonUserDetails errors
  Future<Either<Failure, AppUser>> _createUserWithAlternativeMethod({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required UserRole role,
  }) async {
    try {
      debugPrint('Using alternative user creation method');
      
      // Create minimal user data first
      final profile = UserProfile(
        firstName: firstName,
        lastName: lastName,
        employeeId: _generateEmployeeId(),
      );

      final permissions = _getRolePermissions(role);
      final initialStatus = _getInitialStatusForRole(role);
      final departmentIds = _getDefaultDepartmentIds(role);

      // Try to create Firebase user with minimal operations
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(AuthFailure('Alternative user creation failed'));
      }

      final user = credential.user!;
      debugPrint('Alternative Firebase user created: ${user.uid}');

      // Create AppUser object
      final userData = AppUser(
        id: user.uid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: user.uid,
        email: email,
        displayName: profile.fullName,
        emailVerified: user.emailVerified,
        role: role,
        status: initialStatus,
        profile: profile,
        permissions: permissions,
        departmentIds: departmentIds,
      );

      // Save to Firestore with minimal data approach
      await _saveUserDataSafely(userData);

      // Send email verification (non-critical)
      try {
        await user.sendEmailVerification();
        debugPrint('Email verification sent');
      } catch (e) {
        debugPrint('Warning: Email verification failed: $e');
      }

      debugPrint('Alternative user creation completed successfully');
      return Right(userData);
      
    } catch (e) {
      debugPrint('Alternative user creation failed: $e');
      return Left(AuthFailure('User creation failed: ${e.toString()}'));
    }
  }

  /// Safe user document creation with error handling
  Future<Either<Failure, AppUser>> _createUserDocumentSafely({
    required User user,
    required String email,
    required String firstName,
    required String lastName,
    required UserRole role,
  }) async {
    try {
      debugPrint('Creating user document safely for ${user.uid}');

      // Create user profile with validation
      final profile = UserProfile(
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        employeeId: _generateEmployeeId(),
      );

      // Get role-based data
      final permissions = _getRolePermissions(role);
      final initialStatus = _getInitialStatusForRole(role);
      final departmentIds = _getDefaultDepartmentIds(role);

      // Create user data
      final userData = AppUser(
        id: user.uid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: user.uid,
        email: email,
        displayName: profile.fullName,
        emailVerified: user.emailVerified,
        role: role,
        status: initialStatus,
        profile: profile,
        permissions: permissions,
        departmentIds: departmentIds,
      );

      // Save to Firestore safely
      await _saveUserDataSafely(userData);

      // Update Firebase Auth display name (non-critical)
      try {
        await user.updateDisplayName(profile.fullName);
        debugPrint('Firebase display name updated');
      } catch (e) {
        debugPrint('Warning: Display name update failed: $e');
      }

      // Send email verification (non-critical)
      try {
        await user.sendEmailVerification();
        debugPrint('Email verification sent');
      } catch (e) {
        debugPrint('Warning: Email verification failed: $e');
      }

      debugPrint('User document created successfully');
      return Right(userData);
      
    } catch (e) {
      debugPrint('Error creating user document: $e');
      
      // Clean up Firebase user if document creation fails
      try {
        await user.delete();
        debugPrint('Firebase user cleaned up after document creation failure');
      } catch (cleanupError) {
        debugPrint('Failed to clean up Firebase user: $cleanupError');
      }
      
      if (e.toString().contains('PigeonUserDetails')) {
        return Left(AuthFailure(_getPigeonUserDetailsErrorMessage()));
      }
      
      return Left(AuthFailure('Failed to create user profile: ${e.toString()}'));
    }
  }

  /// Safe Firestore data saving with multiple fallback strategies
  Future<void> _saveUserDataSafely(AppUser user) async {
    try {
      debugPrint('Saving user data safely for ${user.uid}');
      
      // Strategy 1: Try with complete data using transaction
      try {
        await _firestore.runTransaction((transaction) async {
          final userDoc = _firestore.collection('users').doc(user.uid);
          
          final userData = _createSafeUserData(user);
          transaction.set(userDoc, userData);
        });
        debugPrint('User data saved successfully with transaction');
        return;
      } catch (e) {
        debugPrint('Transaction save failed: $e');
      }
      
      // Strategy 2: Try with complete data using set
      try {
        final userData = _createSafeUserData(user);
        await _firestore.collection('users').doc(user.uid).set(userData);
        debugPrint('User data saved successfully with set');
        return;
      } catch (e) {
        debugPrint('Set save failed: $e');
      }
      
      // Strategy 3: Try with minimal data
      try {
        final minimalData = _createMinimalUserData(user);
        await _firestore.collection('users').doc(user.uid).set(minimalData);
        debugPrint('Minimal user data saved successfully');
        return;
      } catch (e) {
        debugPrint('Minimal save failed: $e');
      }
      
      // Strategy 4: Try with basic data only
      final basicData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName ?? '${user.profile.firstName} ${user.profile.lastName}',
        'role': user.role.value,
        'status': user.status.value,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };
      
      await _firestore.collection('users').doc(user.uid).set(basicData);
      debugPrint('Basic user data saved successfully');
      
    } catch (e) {
      debugPrint('All save strategies failed: $e');
      throw Exception('Failed to save user data: ${e.toString()}');
    }
  }

  /// Create safe user data map with proper serialization
  Map<String, dynamic> _createSafeUserData(AppUser user) {
    try {
      return {
        'id': user.id,
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName ?? user.profile.fullName,
        'emailVerified': user.emailVerified,
        'role': user.role.value,
        'status': user.status.value,
        'profile': _serializeUserProfileSafely(user.profile),
        'permissions': user.permissions,
        'departmentIds': user.departmentIds,
        'currentDepartmentId': user.currentDepartmentId,
        'lastLoginAt': user.lastLoginAt != null ? Timestamp.fromDate(user.lastLoginAt!) : null,
        'lastActiveAt': user.lastActiveAt != null ? Timestamp.fromDate(user.lastActiveAt!) : null,
        'metadata': user.metadata,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };
    } catch (e) {
      debugPrint('Error creating safe user data: $e');
      rethrow;
    }
  }

  /// Create minimal user data for fallback scenarios
  Map<String, dynamic> _createMinimalUserData(AppUser user) {
    return {
      'uid': user.uid,
      'email': user.email,
      'displayName': user.profile.fullName,
      'role': user.role.value,
      'status': user.status.value,
      'permissions': user.permissions,
      'departmentIds': user.departmentIds,
      'profile': {
        'firstName': user.profile.firstName,
        'lastName': user.profile.lastName,
        'employeeId': user.profile.employeeId,
      },
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// Safe user profile serialization with error handling
  Map<String, dynamic> _serializeUserProfileSafely(UserProfile profile) {
    try {
      final serializedProfile = <String, dynamic>{
        'firstName': profile.firstName ?? '',
        'lastName': profile.lastName ?? '',
        'middleName': profile.middleName,
        'employeeId': profile.employeeId,
        'jobTitle': profile.jobTitle,
        'department': profile.department,
        'phoneNumber': profile.phoneNumber,
        'address': profile.address,
        'emergencyContact': profile.emergencyContact,
        'emergencyPhone': profile.emergencyPhone,
        'profileImageUrl': profile.profileImageUrl,
      };

      // Handle dateOfBirth safely
      if (profile.dateOfBirth != null) {
        try {
          serializedProfile['dateOfBirth'] = Timestamp.fromDate(profile.dateOfBirth!);
        } catch (e) {
          debugPrint('Error serializing dateOfBirth: $e');
          serializedProfile['dateOfBirth'] = null;
        }
      }

      // Handle hireDate safely
      if (profile.hireDate != null) {
        try {
          serializedProfile['hireDate'] = Timestamp.fromDate(profile.hireDate!);
        } catch (e) {
          debugPrint('Error serializing hireDate: $e');
          serializedProfile['hireDate'] = null;
        }
      }

      // Handle custom fields safely
      try {
        if (profile.customFields.isNotEmpty) {
          serializedProfile['customFields'] = Map<String, dynamic>.from(profile.customFields);
        } else {
          serializedProfile['customFields'] = <String, dynamic>{};
        }
      } catch (e) {
        debugPrint('Error serializing customFields: $e');
        serializedProfile['customFields'] = <String, dynamic>{};
      }

      return serializedProfile;
    } catch (e) {
      debugPrint('Error in safe profile serialization: $e');
      // Return minimal profile data
      return {
        'firstName': profile.firstName ?? '',
        'lastName': profile.lastName ?? '',
        'employeeId': profile.employeeId,
        'customFields': <String, dynamic>{},
      };
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await _firebaseAuth.signOut();
      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserProfile(UserProfile profile) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      final updatedUser = userData.copyWith(
        profile: profile,
        displayName: profile.fullName,
      );

      await _saveUserDataSafely(updatedUser);
      await user.updateDisplayName(profile.fullName);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserRole({
    required String userId,
    required UserRole role,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(
        role: role,
        permissions: _getRolePermissions(role),
      );

      await _saveUserDataSafely(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserStatus({
    required String userId,
    required CommonStatus status,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(status: status);
      await _saveUserDataSafely(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserPermissions({
    required String userId,
    required List<String> permissions,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(permissions: permissions);
      await _saveUserDataSafely(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> getUserById(String userId) async {
    try {
      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }
      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<AppUser>>> getUsers({
    UserRole? role,
    CommonStatus? status,
    String? departmentId,
    int? limit,
  }) async {
    try {
      Query query = _firestore.collection('users');

      if (role != null) {
        query = query.where('role', isEqualTo: role.value);
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (departmentId != null) {
        query = query.where('departmentIds', arrayContains: departmentId);
      }
      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final users = snapshot.docs
          .map((doc) {
            try {
              return _parseUserFromFirestore(doc);
            } catch (e) {
              debugPrint('Error parsing user ${doc.id}: $e');
              return null;
            }
          })
          .where((user) => user != null)
          .cast<AppUser>()
          .toList();

      return Right(users);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<AppUser>>> searchUsers({
    required String query,
    UserRole? role,
    CommonStatus? status,
    int? limit,
  }) async {
    try {
      Query firestoreQuery = _firestore.collection('users');

      if (role != null) {
        firestoreQuery = firestoreQuery.where('role', isEqualTo: role.value);
      }
      if (status != null) {
        firestoreQuery = firestoreQuery.where('status', isEqualTo: status.value);
      }
      if (limit != null) {
        firestoreQuery = firestoreQuery.limit(limit);
      }

      final snapshot = await firestoreQuery.get();
      final users = snapshot.docs
          .map((doc) {
            try {
              return _parseUserFromFirestore(doc);
            } catch (e) {
              debugPrint('Error parsing user ${doc.id}: $e');
              return null;
            }
          })
          .where((user) => user != null)
          .cast<AppUser>()
          .where((user) =>
              user.profile.fullName.toLowerCase().contains(query.toLowerCase()) ||
              user.email.toLowerCase().contains(query.toLowerCase()) ||
              (user.profile.employeeId?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();

      return Right(users);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUser(String userId) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      await _firestore.collection('users').doc(userId).update({
        'status': CommonStatus.inactive.value,
        'deletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> verifyEmail() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.sendEmailVerification();
      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isEmailVerified() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.reload();
      return Right(user.emailVerified);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> refreshUserData() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.reload();
      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> hasPermission(String permission) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData.hasPermission(permission));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> canAccessDepartment(String departmentId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData.canAccessDepartment(departmentId));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> updateLastActive() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await _firestore.collection('users').doc(user.uid).update({
        'lastActiveAt': FieldValue.serverTimestamp(),
      });

      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Helper methods

  Future<AppUser?> _getUserDataFromFirestore(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();

      if (!doc.exists) {
        debugPrint('User document does not exist for UID: $uid');
        return null;
      }

      return _parseUserFromFirestore(doc);
    } catch (e) {
      debugPrint('Error getting user data from Firestore: $e');
      return null;
    }
  }

  AppUser _parseUserFromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;

      return AppUser(
        id: doc.id,
        createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        uid: data['uid'] ?? doc.id,
        email: data['email'] ?? '',
        displayName: data['displayName'],
        photoURL: data['photoURL'],
        emailVerified: data['emailVerified'] ?? false,
        phoneNumber: data['phoneNumber'],
        role: UserRole.values.firstWhere(
          (role) => role.value == data['role'],
          orElse: () => UserRole.sewingOperator,
        ),
        status: CommonStatus.values.firstWhere(
          (status) => status.value == data['status'],
          orElse: () => CommonStatus.pending,
        ),
        profile: _parseUserProfile(data['profile'] ?? {}),
        permissions: List<String>.from(data['permissions'] ?? []),
        departmentIds: List<String>.from(data['departmentIds'] ?? []),
        currentDepartmentId: data['currentDepartmentId'],
        lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate(),
        lastActiveAt: (data['lastActiveAt'] as Timestamp?)?.toDate(),
        metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      );
    } catch (e) {
      debugPrint('Error parsing user from Firestore: $e');
      rethrow;
    }
  }

  UserProfile _parseUserProfile(Map<String, dynamic> data) {
    try {
      return UserProfile(
        firstName: data['firstName'] ?? '',
        lastName: data['lastName'] ?? '',
        middleName: data['middleName'],
        employeeId: data['employeeId'],
        jobTitle: data['jobTitle'],
        department: data['department'],
        phoneNumber: data['phoneNumber'],
        address: data['address'],
        dateOfBirth: (data['dateOfBirth'] as Timestamp?)?.toDate(),
        hireDate: (data['hireDate'] as Timestamp?)?.toDate(),
        emergencyContact: data['emergencyContact'],
        emergencyPhone: data['emergencyPhone'],
        profileImageUrl: data['profileImageUrl'],
        customFields: Map<String, dynamic>.from(data['customFields'] ?? {}),
      );
    } catch (e) {
      debugPrint('Error parsing user profile: $e');
      // Return minimal profile on error
      return UserProfile(
        firstName: data['firstName'] ?? '',
        lastName: data['lastName'] ?? '',
        employeeId: data['employeeId'],
      );
    }
  }

  Future<void> _updateLastLogin(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
        'lastActiveAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating last login: $e');
    }
  }

  Failure _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return const AuthFailure('No user found with this email address');
      case 'wrong-password':
        return const AuthFailure('Incorrect password');
      case 'email-already-in-use':
        return const AuthFailure('An account already exists with this email address');
      case 'weak-password':
        return const AuthFailure('Password is too weak');
      case 'invalid-email':
        return const AuthFailure('Invalid email address');
      case 'user-disabled':
        return const AuthFailure('This account has been disabled');
      case 'too-many-requests':
        return const AuthFailure('Too many failed attempts. Please try again later');
      case 'operation-not-allowed':
        return const AuthFailure('This operation is not allowed');
      case 'requires-recent-login':
        return const AuthFailure('Please sign in again to complete this action');
      case 'invalid-credential':
        return const AuthFailure('Invalid credentials provided');
      case 'account-exists-with-different-credential':
        return const AuthFailure('Account exists with different sign-in method');
      case 'network-request-failed':
        return const AuthFailure('Network error. Please check your connection');
      case 'user-token-expired':
        return const AuthFailure('Session expired. Please sign in again');
      case 'invalid-user-token':
        return const AuthFailure('Invalid session. Please sign in again');
      case 'user-mismatch':
        return const AuthFailure('User mismatch. Please sign in again');
      default:
        return AuthFailure(e.message ?? 'Authentication failed');
    }
  }

  String _generateEmployeeId() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final random = (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    return 'EMP$year$month$random';
  }

  bool _isValidUserRole(UserRole role) {
    return UserRole.values.contains(role);
  }

  bool _isRoleAllowedForPublicSignup(UserRole role) {
    if (role == UserRole.administrator) {
      return true; // Allow admin creation for development
    }

    return [
      UserRole.sewingOperator,
      UserRole.finishingOperator,
      UserRole.cuttingHelper,
      UserRole.qualityController,
      UserRole.viewer,
    ].contains(role);
  }

  Either<Failure, void> _validateRoleSelection(UserRole role, {bool isPublicSignup = true}) {
    if (!_isValidUserRole(role)) {
      return Left(AuthFailure('Invalid role selected: ${role.displayName}'));
    }

    if (isPublicSignup && !_isRoleAllowedForPublicSignup(role)) {
      return Left(AuthFailure(
          'Role ${role.displayName} requires administrator approval. Please contact your supervisor.'));
    }

    return const Right(null);
  }

  Either<Failure, void> _validateRoleForLogin(UserRole role, CommonStatus status) {
    if (status == CommonStatus.suspended) {
      return const Left(AuthFailure('Account is suspended. Please contact administrator.'));
    }

    if (status == CommonStatus.archived) {
      return const Left(AuthFailure('Account is archived. Please contact administrator.'));
    }

    if (role == UserRole.viewer && status == CommonStatus.pending) {
      return const Left(AuthFailure(
          'Viewer accounts require activation. Please contact administrator.'));
    }

    return const Right(null);
  }

  bool _requiresAdminApproval(UserRole role) {
    return [
      UserRole.administrator,
      UserRole.merchandiser,
      UserRole.inventoryManager,
      UserRole.cuttingHead,
      UserRole.sewingHead,
      UserRole.finishingHead,
      UserRole.warehouseManager,
    ].contains(role);
  }

  CommonStatus _getInitialStatusForRole(UserRole role) {
    if (_requiresAdminApproval(role)) {
      return CommonStatus.pending;
    }
    return CommonStatus.active;
  }

  List<String> _getRolePermissions(UserRole role) {
    return role.permissions;
  }

  List<String> _getDefaultDepartmentIds(UserRole role) {
    final department = role.department;
    return [department.value];
  }

  String _getPigeonUserDetailsErrorMessage() {
    return '''
Platform compatibility issue detected. This usually happens when:
1. There's a version mismatch between Firebase plugins
2. The app needs to be restarted
3. There's a temporary platform communication issue

Please try:
1. Restart the app
2. Check your internet connection
3. Try again in a few minutes
4. Contact support if the issue persists
''';
  }

  @override
  Future<Either<Failure, List<UserSession>>> getUserSessions(String userId) async {
    return const Left(UnimplementedFailure('Get user sessions not implemented'));
  }

  @override
  Future<Either<Failure, void>> terminateSession(String sessionId) async {
    return const Left(UnimplementedFailure('Terminate session not implemented'));
  }

  @override
  Future<Either<Failure, void>> terminateAllOtherSessions() async {
    return const Left(UnimplementedFailure('Terminate all other sessions not implemented'));
  }

  @override
  Future<Either<Failure, AppUser>> createInitialAdmin({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    try {
      debugPrint('Creating initial admin user: $email');

      // Check if any admin users already exist
      final existingAdmins = await _checkIfAdminExists();
      if (existingAdmins) {
        return const Left(AuthFailure(
            'Admin user already exists. This method can only be used once.'));
      }

      // Use the safe signup method with admin role
      return await signUpWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        role: UserRole.administrator,
      );
    } catch (e) {
      debugPrint('Unexpected error during admin creation: $e');
      return Left(ErrorHandler.handleException(e));
    }
  }

  Future<bool> _checkIfAdminExists() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: UserRole.administrator.value)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking for existing admin: $e');
      return false;
    }
  }
}