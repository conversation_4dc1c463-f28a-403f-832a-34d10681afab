import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';

import '../../../errors/error_handler.dart';
import '../../../errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../entities/user_entities.dart';
import '../../repositories/auth_repository.dart';

/// Firebase authentication repository implementation
@LazySingleton(as: AuthRepository)
class FirebaseAuthRepository implements AuthRepository {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  FirebaseAuthRepository(this._firebaseAuth, this._firestore);

  @override
  Stream<AppUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().asyncMap((user) async {
      if (user == null) return null;
      try {
        final userData = await _getUserDataFromFirestore(user.uid);
        return userData;
      } catch (e) {
        return null;
      }
    });
  }

  @override
  Stream<AppUser?> get userDataChanges {
    return _firebaseAuth.authStateChanges().asyncExpand((user) {
      if (user == null) return Stream.value(null);
      return _firestore
          .collection('users')
          .doc(user.uid)
          .snapshots()
          .map((doc) => doc.exists ? _parseUserFromFirestore(doc) : null);
    });
  }

  @override
  Future<Either<Failure, AppUser?>> getCurrentUser() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return const Right(null);

      final userData = await _getUserDataFromFirestore(user.uid);
      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(AuthFailure('Sign in failed'));
      }

      final userData = await _getUserDataFromFirestore(credential.user!.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      // Check if user is active
      if (!userData.status.canLogin) {
        await _firebaseAuth.signOut();
        return Left(AuthFailure('Account is ${userData.status.displayName.toLowerCase()}'));
      }

      // Update last login timestamp
      await _updateLastLogin(userData.uid);

      return Right(userData);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    UserRole role = UserRole.operator,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(AuthFailure('Sign up failed'));
      }

      final user = credential.user!;

      // Create user profile
      final profile = UserProfile(
        firstName: firstName,
        lastName: lastName,
        employeeId: _generateEmployeeId(),
      );

      // Create user data
      final userData = AppUser(
        id: user.uid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: user.uid,
        email: user.email!,
        displayName: profile.fullName,
        emailVerified: user.emailVerified,
        role: role,
        status: UserStatus.pending, // Require admin approval
        profile: profile,
        permissions: role.defaultPermissions,
      );

      // Save user data to Firestore
      await _saveUserDataToFirestore(userData);

      // Update Firebase user display name
      await user.updateDisplayName(profile.fullName);

      // Send email verification
      await user.sendEmailVerification();

      return Right(userData);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await _firebaseAuth.signOut();
      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(_handleFirebaseAuthException(e));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserProfile(UserProfile profile) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      final updatedUser = userData.copyWith(
        profile: profile,
        displayName: profile.fullName,
      );

      await _saveUserDataToFirestore(updatedUser);
      await user.updateDisplayName(profile.fullName);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserRole({
    required String userId,
    required UserRole role,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(
        role: role,
        permissions: role.defaultPermissions,
      );

      await _saveUserDataToFirestore(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserStatus({
    required String userId,
    required UserStatus status,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(status: status);
      await _saveUserDataToFirestore(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> updateUserPermissions({
    required String userId,
    required List<String> permissions,
  }) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }

      final updatedUser = userData.copyWith(permissions: permissions);
      await _saveUserDataToFirestore(updatedUser);

      return Right(updatedUser);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> getUserById(String userId) async {
    try {
      final userData = await _getUserDataFromFirestore(userId);
      if (userData == null) {
        return const Left(AuthFailure('User not found'));
      }
      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<AppUser>>> getUsers({
    UserRole? role,
    UserStatus? status,
    String? departmentId,
    int? limit,
  }) async {
    try {
      Query query = _firestore.collection('users');

      if (role != null) {
        query = query.where('role', isEqualTo: role.value);
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (departmentId != null) {
        query = query.where('departmentIds', arrayContains: departmentId);
      }
      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final users = snapshot.docs.map((doc) => _parseUserFromFirestore(doc)).toList();

      return Right(users);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, List<AppUser>>> searchUsers({
    required String query,
    UserRole? role,
    UserStatus? status,
    int? limit,
  }) async {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a simplified implementation
      Query firestoreQuery = _firestore.collection('users');

      if (role != null) {
        firestoreQuery = firestoreQuery.where('role', isEqualTo: role.value);
      }
      if (status != null) {
        firestoreQuery = firestoreQuery.where('status', isEqualTo: status.value);
      }
      if (limit != null) {
        firestoreQuery = firestoreQuery.limit(limit);
      }

      final snapshot = await firestoreQuery.get();
      final users = snapshot.docs
          .map((doc) => _parseUserFromFirestore(doc))
          .where((user) =>
              user.profile.fullName.toLowerCase().contains(query.toLowerCase()) ||
              user.email.toLowerCase().contains(query.toLowerCase()) ||
              (user.profile.employeeId?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();

      return Right(users);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUser(String userId) async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final currentUserData = await _getUserDataFromFirestore(currentUser.uid);
      if (currentUserData == null || !currentUserData.isAdmin) {
        return const Left(AuthFailure('Insufficient permissions'));
      }

      // Soft delete by updating status
      await _firestore.collection('users').doc(userId).update({
        'status': UserStatus.inactive.value,
        'deletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> verifyEmail() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.sendEmailVerification();
      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isEmailVerified() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.reload();
      return Right(user.emailVerified);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, AppUser>> refreshUserData() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await user.reload();
      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> hasPermission(String permission) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData.hasPermission(permission));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, bool>> canAccessDepartment(String departmentId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      final userData = await _getUserDataFromFirestore(user.uid);
      if (userData == null) {
        return const Left(AuthFailure('User data not found'));
      }

      return Right(userData.canAccessDepartment(departmentId));
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Future<Either<Failure, void>> updateLastActive() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(AuthFailure('User not authenticated'));
      }

      await _firestore.collection('users').doc(user.uid).update({
        'lastActiveAt': FieldValue.serverTimestamp(),
      });

      return const Right(null);
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  // Helper methods

  Future<AppUser?> _getUserDataFromFirestore(String uid) async {
    final doc = await _firestore.collection('users').doc(uid).get();
    if (!doc.exists) return null;
    return _parseUserFromFirestore(doc);
  }

  AppUser _parseUserFromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AppUser(
      id: doc.id,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      uid: data['uid'] ?? doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      emailVerified: data['emailVerified'] ?? false,
      phoneNumber: data['phoneNumber'],
      role: UserRole.values.firstWhere(
        (role) => role.value == data['role'],
        orElse: () => UserRole.operator,
      ),
      status: UserStatus.values.firstWhere(
        (status) => status.value == data['status'],
        orElse: () => UserStatus.pending,
      ),
      profile: _parseUserProfile(data['profile'] ?? {}),
      permissions: List<String>.from(data['permissions'] ?? []),
      departmentIds: List<String>.from(data['departmentIds'] ?? []),
      currentDepartmentId: data['currentDepartmentId'],
      lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate(),
      lastActiveAt: (data['lastActiveAt'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  UserProfile _parseUserProfile(Map<String, dynamic> data) {
    return UserProfile(
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      middleName: data['middleName'],
      employeeId: data['employeeId'],
      jobTitle: data['jobTitle'],
      department: data['department'],
      phoneNumber: data['phoneNumber'],
      address: data['address'],
      dateOfBirth: (data['dateOfBirth'] as Timestamp?)?.toDate(),
      hireDate: (data['hireDate'] as Timestamp?)?.toDate(),
      emergencyContact: data['emergencyContact'],
      emergencyPhone: data['emergencyPhone'],
      customFields: Map<String, dynamic>.from(data['customFields'] ?? {}),
    );
  }

  Future<void> _saveUserDataToFirestore(AppUser user) async {
    await _firestore.collection('users').doc(user.uid).set({
      'uid': user.uid,
      'email': user.email,
      'displayName': user.displayName,
      'photoURL': user.photoURL,
      'emailVerified': user.emailVerified,
      'phoneNumber': user.phoneNumber,
      'role': user.role.value,
      'status': user.status.value,
      'profile': _serializeUserProfile(user.profile),
      'permissions': user.permissions,
      'departmentIds': user.departmentIds,
      'currentDepartmentId': user.currentDepartmentId,
      'lastLoginAt': user.lastLoginAt != null ? Timestamp.fromDate(user.lastLoginAt!) : null,
      'lastActiveAt': user.lastActiveAt != null ? Timestamp.fromDate(user.lastActiveAt!) : null,
      'metadata': user.metadata,
      'createdAt': Timestamp.fromDate(user.createdAt),
      'updatedAt': FieldValue.serverTimestamp(),
    }, SetOptions(merge: true));
  }

  Map<String, dynamic> _serializeUserProfile(UserProfile profile) {
    return {
      'firstName': profile.firstName,
      'lastName': profile.lastName,
      'middleName': profile.middleName,
      'employeeId': profile.employeeId,
      'jobTitle': profile.jobTitle,
      'department': profile.department,
      'phoneNumber': profile.phoneNumber,
      'address': profile.address,
      'dateOfBirth': profile.dateOfBirth != null ? Timestamp.fromDate(profile.dateOfBirth!) : null,
      'hireDate': profile.hireDate != null ? Timestamp.fromDate(profile.hireDate!) : null,
      'emergencyContact': profile.emergencyContact,
      'emergencyPhone': profile.emergencyPhone,
      'customFields': profile.customFields,
    };
  }

  Future<void> _updateLastLogin(String uid) async {
    await _firestore.collection('users').doc(uid).update({
      'lastLoginAt': FieldValue.serverTimestamp(),
      'lastActiveAt': FieldValue.serverTimestamp(),
    });
  }

  String _generateEmployeeId() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final random = (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    return 'EMP$year$month$random';
  }

  Failure _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return const AuthFailure('No user found with this email address');
      case 'wrong-password':
        return const AuthFailure('Incorrect password');
      case 'email-already-in-use':
        return const AuthFailure('An account already exists with this email address');
      case 'weak-password':
        return const AuthFailure('Password is too weak');
      case 'invalid-email':
        return const AuthFailure('Invalid email address');
      case 'user-disabled':
        return const AuthFailure('This account has been disabled');
      case 'too-many-requests':
        return const AuthFailure('Too many failed attempts. Please try again later');
      case 'operation-not-allowed':
        return const AuthFailure('This operation is not allowed');
      case 'requires-recent-login':
        return const AuthFailure('Please sign in again to complete this action');
      default:
        return AuthFailure(e.message ?? 'Authentication failed');
    }
  }

  // Unimplemented methods
  @override
  Future<Either<Failure, List<UserSession>>> getUserSessions(String userId) async {
    return const Left(UnimplementedFailure('Get user sessions not implemented'));
  }

  @override
  Future<Either<Failure, void>> terminateSession(String sessionId) async {
    return const Left(UnimplementedFailure('Terminate session not implemented'));
  }

  @override
  Future<Either<Failure, void>> terminateAllOtherSessions() async {
    return const Left(UnimplementedFailure('Terminate all other sessions not implemented'));
  }
}
