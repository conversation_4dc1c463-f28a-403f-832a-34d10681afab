import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/auth/presentation/pages/firebase_login_page.dart';

import '../../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../../features/auth/presentation/pages/login_page.dart';
import '../../../shared/widgets/loading_widget.dart';

/// Auth guard widget that protects routes requiring authentication
class AuthGuard extends StatelessWidget {
  final Widget child;

  const AuthGuard({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: GetIt.instance<AuthBloc>(),
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is AuthLoading || state is AuthInitial) {
            return const Scaffold(
              body: Center(
                child: LoadingWidget(),
              ),
            );
          } else if (state is AuthAuthenticated) {
            return child;
          } else {
            // User is not authenticated, show login page
            return const FirebaseLoginPage();
          }
        },
      ),
    );
  }
}
