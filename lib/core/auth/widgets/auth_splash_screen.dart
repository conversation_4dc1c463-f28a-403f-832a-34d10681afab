import 'package:flutter/material.dart';
import 'package:hm_collection/features/auth/presentation/pages/firebase_login_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/administrator_dashboard_page.dart';
import 'package:provider/provider.dart';

import '../../../features/auth/presentation/pages/admin_login_page.dart';
import '../../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../providers/auth_provider.dart';

class AuthSplashScreen extends StatelessWidget {
  const AuthSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        switch (authProvider.authState) {
          case AuthState.authenticated:
            return const AdministratorDashboardPage();
          case AuthState.unauthenticated:
            return const FirebaseLoginPage();
          case AuthState.loading:
          default:
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    <PERSON><PERSON><PERSON><PERSON>(height: 20),
                    Text('Initializing...'),
                  ],
                ),
              ),
            );
        }
      },
    );
  }
}
