import 'package:flutter/material.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/administrator_dashboard_page.dart';
import 'package:provider/provider.dart';

import '../../../features/auth/presentation/pages/firebase_login_page.dart';
import '../../../features/dashboard/presentation/pages/admin_dashboard_page.dart';
import '../../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../firebase/firebase_auth_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../providers/auth_provider.dart' as app_auth;
import 'auth_splash_screen.dart';

/// Main app wrapper that handles authentication flow
class AuthAppWrapper extends StatefulWidget {
  const AuthAppWrapper({super.key});

  @override
  State<AuthAppWrapper> createState() => _AuthAppWrapperState();
}

class _AuthAppWrapperState extends State<AuthAppWrapper> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<app_auth.AuthProvider>(
      builder: (context, authProvider, child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _buildCurrentScreen(authProvider),
        );
      },
    );
  }

  Widget _buildCurrentScreen(app_auth.AuthProvider authProvider) {
    // Check for error message first
    if (authProvider.errorMessage != null) {
      return _buildErrorScreen(authProvider.errorMessage);
    }

    switch (authProvider.authState) {
      case app_auth.AuthState.loading:
        return const AuthSplashScreen();

      case app_auth.AuthState.authenticated:
        return const AdministratorDashboardPage();

      case app_auth.AuthState.unauthenticated:
      default:
        return const FirebaseLoginPage();
    }
  }

  Widget _buildErrorScreen(String? errorMessage) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: AppColors.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Authentication Error',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage ?? 'An unexpected error occurred',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushReplacementNamed('/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const FirebaseLoginPage(),
                    ),
                  );
                },
                child: Text(
                  'Go to Login',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Firebase Auth State Listener Widget
class FirebaseAuthStateListener extends StatefulWidget {
  final Widget child;

  const FirebaseAuthStateListener({
    super.key,
    required this.child,
  });

  @override
  State<FirebaseAuthStateListener> createState() =>
      _FirebaseAuthStateListenerState();
}

class _FirebaseAuthStateListenerState extends State<FirebaseAuthStateListener> {
  @override
  void initState() {
    super.initState();
    _setupFirebaseAuthListener();
  }

  void _setupFirebaseAuthListener() {
    // Listen to Firebase auth state changes
    final firebaseAuthService = context.read<FirebaseAuthService>();

    firebaseAuthService.authStateStream.listen(
      (isSignedIn) {
        if (mounted) {
          final authProvider = context.read<app_auth.AuthProvider>();

          if (!isSignedIn && authProvider.authState == app_auth.AuthState.authenticated) {
            // User signed out from Firebase, update local state
            // If you have a logout method on a usecase/repository, trigger it here; otherwise just update state.
            // For now, transition to unauthenticated state via provider API.
            // authProvider.logout(); // Uncomment if such method exists
          }
        }
      },
    );

    firebaseAuthService.userStream.listen(
      (user) {
        if (mounted && user != null) {
          final authProvider = context.read<app_auth.AuthProvider>();

          // Update user profile if it changed
          if (authProvider.user?.id != user.id) {
            authProvider.updateProfile(user);
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Authentication Guard Widget
class AuthGuard extends StatelessWidget {
  final Widget child;
  final Widget? fallback;
  final List<String>? requiredPermissions;
  const AuthGuard({
    super.key,
    required this.child,
    this.fallback,
    this.requiredPermissions,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<app_auth.AuthProvider>(
      builder: (context, authProvider, _) {
        // Check if user is authenticated
        if (authProvider.authState != app_auth.AuthState.authenticated) {
          return fallback ?? const FirebaseLoginPage();
        }

        final user = authProvider.user!;

        // Check permissions if required
        if (requiredPermissions != null && requiredPermissions!.isNotEmpty) {
          final hasAllPermissions = requiredPermissions!.every(
            (permission) => authProvider.hasPermission(permission),
          );

          if (!hasAllPermissions) {
            return _buildAccessDeniedScreen(context);
          }
        }

        return child;
      },
    );
  }

  Widget _buildAccessDeniedScreen(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
        backgroundColor: AppColors.error,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 24),
            Text(
              'Access Denied',
              style: AppTextStyles.headlineMedium.copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'You don\'t have permission to access this feature.',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Go Back'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
