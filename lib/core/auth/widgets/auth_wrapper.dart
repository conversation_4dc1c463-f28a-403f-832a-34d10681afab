import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/administrator_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/inventory_manager_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/merchandiser_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/quality_controller_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/sewing_operator_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/sewing_supervisor_dashboard_page.dart';
import 'package:hm_collection/features/dashboard/presentation/pages/viewer_dashboard_page.dart';

import '../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
import '../../../features/auth/presentation/pages/firebase_login_page.dart';
import '../../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../../shared/widgets/loading_screen.dart';
import '../../../features/auth/domain/entities/user.dart';
import '../../../shared/enums/common_enums.dart';

/// Authentication wrapper that handles the authentication flow
class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          GetIt.instance<FirebaseAuthBloc>()..add(const FirebaseAuthStarted()),
      child: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthLoading || state is FirebaseAuthInitial) {
            return const LoadingScreen();
          } else if (state is FirebaseAuthAuthenticated) {
            return _buildAuthenticatedApp(context, state.user);
          } else {
            return const FirebaseLoginPage();
          }
        },
      ),
    );
  }

  Widget _buildAuthenticatedApp(BuildContext context, User user) {
    // Check if user account is active
    if (!user.isActive) {
      return _buildInactiveAccountScreen(context, user);
    }

    // Check if email is verified (optional requirement)
    if (!user.isEmailVerified) {
      return _buildEmailVerificationScreen(user);
    }

    // User is authenticated and active, show main app
    return const AdministratorDashboardPage();
  }

  Widget _buildInactiveAccountScreen(BuildContext context, User user) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getStatusIcon(user.status),
                size: 80,
                color: _getStatusColor(user.status),
              ),
              const SizedBox(height: 24),
              Text(
                'Account ${user.status.displayName}',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _getStatusMessage(user.status),
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<FirebaseAuthBloc>()
                      .add(const FirebaseSignOutRequested());
                },
                child: const Text('Sign Out'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmailVerificationScreen(User user) {
    return Center(
      child: Builder(
        builder: (BuildContext context) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.email_outlined,
                size: 64,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              const Text(
                'Verify Your Email',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Text(
                  'Please check your email (${user.email}) and click the verification link to continue.',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<FirebaseAuthBloc>()
                      .add(const FirebaseSendEmailVerificationRequested());
                },
                child: const Text('Resend Verification Email'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  context
                      .read<FirebaseAuthBloc>()
                      .add(const RefreshUserDataRequested());
                },
                child: const Text('I\'ve verified my email'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  context
                      .read<FirebaseAuthBloc>()
                      .add(const FirebaseSignOutRequested());
                },
                child: const Text('Sign Out'),
              ),
            ],
          );
        },
      ),
    );
  }

  IconData _getStatusIcon(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return Icons.check_circle;
      case CommonStatus.inactive:
        return Icons.pause_circle;
      case CommonStatus.suspended:
        return Icons.block;
      case CommonStatus.pending:
        return Icons.hourglass_empty;
      case CommonStatus.archived:
        return Icons.archive;
    }
  }

  Color _getStatusColor(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return Colors.green;
      case CommonStatus.inactive:
        return Colors.grey;
      case CommonStatus.suspended:
        return Colors.red;
      case CommonStatus.pending:
        return Colors.orange;
      case CommonStatus.archived:
        return Colors.grey.withValues(alpha: 0.5);
    }
  }

  String _getStatusMessage(CommonStatus status) {
    switch (status) {
      case CommonStatus.active:
        return 'Your account is active and ready to use.';
      case CommonStatus.inactive:
        return 'Your account is currently inactive. Please contact your administrator.';
      case CommonStatus.suspended:
        return 'Your account has been suspended. Please contact your administrator for assistance.';
      case CommonStatus.pending:
        return 'Your account is pending approval. Please wait for an administrator to activate your account.';
      case CommonStatus.archived:
        return 'Your account has been archived. Please contact your administrator.';
    }
  }
}

/// Role-based navigation wrapper
class RoleBasedNavigationWrapper extends StatelessWidget {
  final User user;

  const RoleBasedNavigationWrapper({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    // Navigate to different screens based on user role
    switch (user.role) {
      case UserRole.administrator:
        return const AdministratorDashboardPage(); // Admin dashboard
      case UserRole.merchandiser:
        return const MerchandiserDashboardPage(); // Manager dashboard
      case UserRole.sewingSupervisor:
        return const SewingSupervisorDashboardPage(); // Supervisor dashboard
      case UserRole.sewingOperator:
        return const SewingOperatorDashboardPage(); // Operator dashboard
      case UserRole.qualityController:
        return const QualityControllerDashboardPage(); // Inspector dashboard
      case UserRole.viewer:
        return const ViewerDashboardPage(); // Viewer dashboard
      default:
        return const InventoryManagerDashboardPage(); // Default dashboard
    }
  }
}

/// Session timeout wrapper
class SessionTimeoutWrapper extends StatefulWidget {
  final Widget child;
  final Duration timeoutDuration;

  const SessionTimeoutWrapper({
    super.key,
    required this.child,
    this.timeoutDuration = const Duration(minutes: 30),
  });

  @override
  State<SessionTimeoutWrapper> createState() => _SessionTimeoutWrapperState();
}

class _SessionTimeoutWrapperState extends State<SessionTimeoutWrapper>
    with WidgetsBindingObserver {
  DateTime? _lastInteraction;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateLastInteraction();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkSessionTimeout();
    } else if (state == AppLifecycleState.paused) {
      _updateLastInteraction();
    }
  }

  void _updateLastInteraction() {
    _lastInteraction = DateTime.now();
    // Update last active in Firebase
    context.read<FirebaseAuthBloc>().add(const UpdateLastActiveRequested());
  }

  void _checkSessionTimeout() {
    if (_lastInteraction != null) {
      final now = DateTime.now();
      final timeSinceLastInteraction = now.difference(_lastInteraction!);

      if (timeSinceLastInteraction > widget.timeoutDuration) {
        // Session timed out, sign out user
        context.read<FirebaseAuthBloc>().add(const FirebaseSignOutRequested());

        // Show timeout dialog
        _showSessionTimeoutDialog();
      }
    }
  }

  void _showSessionTimeoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Session Expired'),
        content: const Text(
          'Your session has expired due to inactivity. Please sign in again.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to login page
              Navigator.of(context).pushReplacementNamed('/login');
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _updateLastInteraction,
      onPanDown: (_) => _updateLastInteraction(),
      onScaleStart: (_) => _updateLastInteraction(),
      child: widget.child,
    );
  }
}
