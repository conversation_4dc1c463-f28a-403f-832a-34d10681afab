import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
import '../entities/user_entities.dart' hide UserRole, UserStatus;
import '../../../shared/enums/common_enums.dart';

/// Widget that shows/hides content based on user roles
class RoleBasedWidget extends StatelessWidget {
  final Widget child;
  final List<UserRole> allowedRoles;
  final Widget? fallback;
  final bool showFallbackWhenUnauthenticated;

  const RoleBasedWidget({
    super.key,
    required this.child,
    required this.allowedRoles,
    this.fallback,
    this.showFallbackWhenUnauthenticated = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          if (allowedRoles.contains(state.user.role)) {
            return child;
          }
        } else if (showFallbackWhenUnauthenticated) {
          return fallback ?? const SizedBox.shrink();
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget that shows content only to admin users
class AdminOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const AdminOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleBasedWidget(
      allowedRoles: const [UserRole.administrator],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget that shows content to managers and above
class ManagerAndAboveWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const ManagerAndAboveWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleBasedWidget(
      allowedRoles: const [UserRole.administrator, UserRole.merchandiser],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget that shows content to supervisors and above
class SupervisorAndAboveWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const SupervisorAndAboveWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleBasedWidget(
      allowedRoles: const [UserRole.administrator, UserRole.merchandiser, UserRole.sewingSupervisor],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget that shows content based on specific permissions
class PermissionBasedWidget extends StatelessWidget {
  final Widget child;
  final List<String> requiredPermissions;
  final bool requireAll; // If true, user must have ALL permissions. If false, user needs ANY permission
  final Widget? fallback;

  const PermissionBasedWidget({
    super.key,
    required this.child,
    required this.requiredPermissions,
    this.requireAll = true,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          final user = state.user;
          
          bool hasPermission;
          if (requireAll) {
            hasPermission = requiredPermissions.every((permission) => 
              user.hasPermission(permission));
          } else {
            hasPermission = requiredPermissions.any((permission) => 
              user.hasPermission(permission));
          }
          
          if (hasPermission) {
            return child;
          }
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget that shows content based on user status
class StatusBasedWidget extends StatelessWidget {
  final Widget child;
  final List<CommonStatus> allowedStatuses;
  final Widget? fallback;

  const StatusBasedWidget({
    super.key,
    required this.child,
    required this.allowedStatuses,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          if (allowedStatuses.contains(state.user.status)) {
            return child;
          }
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget that shows content only to active users
class ActiveUserOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const ActiveUserOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return StatusBasedWidget(
      allowedStatuses: const [CommonStatus.active],
      fallback: fallback,
      child: child,
    );
  }
}

/// Widget that conditionally shows content based on user authentication
class AuthenticatedWidget extends StatelessWidget {
  final Widget authenticatedChild;
  final Widget? unauthenticatedChild;

  const AuthenticatedWidget({
    super.key,
    required this.authenticatedChild,
    this.unauthenticatedChild,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          return authenticatedChild;
        }
        
        return unauthenticatedChild ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget that shows different content based on user role
class RoleBasedSwitcher extends StatelessWidget {
  final Map<UserRole, Widget> roleWidgets;
  final Widget? defaultWidget;

  const RoleBasedSwitcher({
    super.key,
    required this.roleWidgets,
    this.defaultWidget,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          final widget = roleWidgets[state.user.role];
          if (widget != null) {
            return widget;
          }
        }
        
        return defaultWidget ?? const SizedBox.shrink();
      },
    );
  }
}

/// Button that is enabled/disabled based on permissions
class PermissionBasedButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final List<String> requiredPermissions;
  final bool requireAll;
  final String? disabledTooltip;
  final ButtonStyle? style;

  const PermissionBasedButton({
    super.key,
    required this.child,
    required this.onPressed,
    required this.requiredPermissions,
    this.requireAll = true,
    this.disabledTooltip,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        bool isEnabled = false;
        
        if (state is FirebaseAuthAuthenticated) {
          final user = state.user;
          
          if (requireAll) {
            isEnabled = requiredPermissions.every((permission) => 
              user.hasPermission(permission));
          } else {
            isEnabled = requiredPermissions.any((permission) => 
              user.hasPermission(permission));
          }
        }
        
        final button = ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: style,
          child: child,
        );
        
        if (!isEnabled && disabledTooltip != null) {
          return Tooltip(
            message: disabledTooltip!,
            child: button,
          );
        }
        
        return button;
      },
    );
  }
}

/// Icon button that is enabled/disabled based on permissions
class PermissionBasedIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final List<String> requiredPermissions;
  final bool requireAll;
  final String? disabledTooltip;
  final Color? color;
  final double? iconSize;

  const PermissionBasedIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.requiredPermissions,
    this.requireAll = true,
    this.disabledTooltip,
    this.color,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        bool isEnabled = false;
        
        if (state is FirebaseAuthAuthenticated) {
          final user = state.user;
          
          if (requireAll) {
            isEnabled = requiredPermissions.every((permission) => 
              user.hasPermission(permission));
          } else {
            isEnabled = requiredPermissions.any((permission) => 
              user.hasPermission(permission));
          }
        }
        
        final button = IconButton(
          onPressed: isEnabled ? onPressed : null,
          icon: Icon(icon),
          color: isEnabled ? color : Colors.grey,
          iconSize: iconSize,
        );
        
        if (!isEnabled && disabledTooltip != null) {
          return Tooltip(
            message: disabledTooltip!,
            child: button,
          );
        }
        
        return button;
      },
    );
  }
}

/// Menu item that is shown/hidden based on permissions
class PermissionBasedMenuItem extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final List<String> requiredPermissions;
  final bool requireAll;

  const PermissionBasedMenuItem({
    super.key,
    required this.child,
    required this.onTap,
    required this.requiredPermissions,
    this.requireAll = true,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionBasedWidget(
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      child: ListTile(
        onTap: onTap,
        title: child,
      ),
    );
  }
}

/// Tab that is shown/hidden based on permissions
class PermissionBasedTab extends StatelessWidget {
  final Widget tab;
  final Widget tabView;
  final List<String> requiredPermissions;
  final bool requireAll;

  const PermissionBasedTab({
    super.key,
    required this.tab,
    required this.tabView,
    required this.requiredPermissions,
    this.requireAll = true,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionBasedWidget(
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      child: Column(
        children: [tab, Expanded(child: tabView)],
      ),
    );
  }
}

/// Floating action button that is shown/hidden based on permissions
class PermissionBasedFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final List<String> requiredPermissions;
  final bool requireAll;
  final String? tooltip;

  const PermissionBasedFAB({
    super.key,
    required this.onPressed,
    required this.child,
    required this.requiredPermissions,
    this.requireAll = true,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionBasedWidget(
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        child: child,
      ),
    );
  }
}
