import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../features/auth/domain/entities/user.dart';
import '../../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../../features/auth/presentation/bloc/firebase_auth_bloc.dart';
import '../entities/user_entities.dart';
import '../../../shared/enums/common_enums.dart';

/// Permission guard widget that shows/hides content based on user permissions
class PermissionGuard extends StatelessWidget {
  final String? permission;
  final UserRole? requiredRole;
  final List<UserRole>? allowedRoles;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnError;

  const PermissionGuard({
    super.key,
    this.permission,
    this.requiredRole,
    this.allowedRoles,
    required this.child,
    this.fallback,
    this.showFallbackOnError = true,
  }) : assert(
          permission != null || requiredRole != null || allowedRoles != null,
          'At least one of permission, requiredRole, or allowedRoles must be provided',
        );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is FirebaseAuthAuthenticated) {
          final user = state.user;
          
          // Check permission if provided
          if (permission != null && !user.hasPermission(permission!)) {
            return fallback ?? const SizedBox.shrink();
          }
          
          // Check required role if provided
          if (requiredRole != null && user.role != requiredRole) {
            // Check if user has higher priority role
            if (user.role.priority < requiredRole!.priority) {
              return fallback ?? const SizedBox.shrink();
            }
          }
          
          // Check allowed roles if provided
          if (allowedRoles != null && !allowedRoles!.contains(user.role)) {
            return fallback ?? const SizedBox.shrink();
          }
          
          return child;
        }
        
        // Show fallback if not authenticated and showFallbackOnError is true
        if (showFallbackOnError) {
          return fallback ?? const SizedBox.shrink();
        }
        
        return const SizedBox.shrink();
      },
    );
  }
}

/// Role guard widget that shows/hides content based on user role
class RoleGuard extends StatelessWidget {
  final UserRole requiredRole;
  final Widget child;
  final Widget? fallback;
  final bool allowHigherRoles;

  const RoleGuard({
    super.key,
    required this.requiredRole,
    required this.child,
    this.fallback,
    this.allowHigherRoles = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          final user = state.user;
          
          if (allowHigherRoles) {
            // Allow if user has required role or higher priority role
            if (user.role.priority >= requiredRole.priority) {
              return child;
            }
          } else {
            // Only allow exact role match
            if (user.role == requiredRole) {
              return child;
            }
          }
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Admin only widget
class AdminOnly extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const AdminOnly({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.administrator,
      allowHigherRoles: false,
      fallback: fallback,
      child: child,
    );
  }
}

/// Manager or above widget
class ManagerOrAbove extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const ManagerOrAbove({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.merchandiser,
      allowHigherRoles: true,
      fallback: fallback,
      child: child,
    );
  }
}

/// Supervisor or above widget
class SupervisorOrAbove extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const SupervisorOrAbove({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.sewingSupervisor,
      allowHigherRoles: true,
      fallback: fallback,
      child: child,
    );
  }
}

/// Department access guard widget
class DepartmentGuard extends StatelessWidget {
  final String departmentId;
  final Widget child;
  final Widget? fallback;

  const DepartmentGuard({
    super.key,
    required this.departmentId,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          final user = state.user;
          
          if (user.department.value == departmentId) {
            return child;
          }
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Conditional widget based on user status
class StatusGuard extends StatelessWidget {
  final CommonStatus requiredStatus;
  final Widget child;
  final Widget? fallback;

  const StatusGuard({
    super.key,
    required this.requiredStatus,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          final user = state.user;
          
          if (user.status == requiredStatus) {
            return child;
          }
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Active user only widget
class ActiveUserOnly extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const ActiveUserOnly({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return StatusGuard(
      requiredStatus: CommonStatus.active,
      fallback: fallback,
      child: child,
    );
  }
}

/// Permission checker mixin for widgets
mixin PermissionChecker {
  bool hasPermission(BuildContext context, String permission) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.hasPermission(permission);
    }
    
    return false;
  }
  
  bool hasRole(BuildContext context, UserRole role) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.role == role;
    }
    
    return false;
  }
  
  bool hasRoleOrAbove(BuildContext context, UserRole role) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.role.priority >= role.priority;
    }
    
    return false;
  }
  
  /// Checks if the current user can access the specified department by ID
  /// 
  /// [context] - The build context
  /// [departmentId] - The department ID to check access for
  /// Returns true if the user has access to the department, false otherwise
  bool canAccessDepartment(BuildContext context, String departmentId) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.canAccessDepartment(departmentId) ||
             state.user.hasPermission('access_all_departments');
    }
    
    return false;
  }
  
  bool isActive(BuildContext context) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.isActive;
    }
    
    return false;
  }
  
  User? getCurrentUser(BuildContext context) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user;
    }
    
    return null;
  }
}

/// Permission helper class for programmatic permission checks
class PermissionHelper {
  static bool hasPermission(BuildContext context, String permission) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.hasPermission(permission);
    }
    
    return false;
  }
  
  static bool hasRole(BuildContext context, UserRole role) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.role == role;
    }
    
    return false;
  }
  
  static bool hasRoleOrAbove(BuildContext context, UserRole role) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.role.priority >= role.priority;
    }
    
    return false;
  }
  
  /// Checks if the current user can access the specified department by ID
  /// 
  /// [context] - The build context
  /// [departmentId] - The department ID to check access for
  /// Returns true if the user has access to the department, false otherwise
  static bool canAccessDepartment(BuildContext context, String departmentId) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user.canAccessDepartment(departmentId);
    }
    
    return false;
  }
  
  static bool isAdmin(BuildContext context) {
    return hasRole(context, UserRole.administrator);
  }

  static bool isManager(BuildContext context) {
    return hasRole(context, UserRole.merchandiser);
  }

  static bool isSupervisor(BuildContext context) {
    return hasRole(context, UserRole.sewingSupervisor);
  }

  static bool isOperator(BuildContext context) {
    return hasRole(context, UserRole.sewingOperator);
  }

  static bool isInspector(BuildContext context) {
    return hasRole(context, UserRole.qualityController);
  }

  static bool isViewer(BuildContext context) {
    return hasRole(context, UserRole.viewer);
  }
  
  static User? getCurrentUser(BuildContext context) {
    final authBloc = context.read<AuthBloc>();
    final state = authBloc.state;
    
    if (state is AuthAuthenticated) {
      return state.user;
    }
    
    return null;
  }
}
