import 'package:dartz/dartz.dart';

import '../../errors/failures.dart';
import '../../../shared/models/api_response.dart';
import '../entities/user_entities.dart';
import '../../../shared/enums/common_enums.dart';

/// Authentication repository interface
abstract class AuthRepository {
  /// Get current user
  Future<Either<Failure, AppUser?>> getCurrentUser();

  /// Sign in with email and password
  Future<Either<Failure, AppUser>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Sign up with email and password
  Future<Either<Failure, AppUser>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    UserRole role = UserRole.sewingOperator,
  });

  /// Sign out
  Future<Either<Failure, void>> signOut();

  /// Send password reset email
  Future<Either<Failure, void>> sendPasswordResetEmail(String email);

  /// Update password
  Future<Either<Failure, void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Update user profile
  Future<Either<Failure, AppUser>> updateUserProfile(UserProfile profile);

  /// Update user role (admin only)
  Future<Either<Failure, AppUser>> updateUserRole({
    required String userId,
    required UserRole role,
  });

  /// Update user status (admin only)
  Future<Either<Failure, AppUser>> updateUserStatus({
    required String userId,
    required CommonStatus status,
  });

  /// Update user permissions (admin only)
  Future<Either<Failure, AppUser>> updateUserPermissions({
    required String userId,
    required List<String> permissions,
  });

  /// Get user by ID
  Future<Either<Failure, AppUser>> getUserById(String userId);

  /// Get users with filtering
  Future<Either<Failure, List<AppUser>>> getUsers({
    UserRole? role,
    CommonStatus? status,
    String? departmentId,
    int? limit,
  });

  /// Search users
  Future<Either<Failure, List<AppUser>>> searchUsers({
    required String query,
    UserRole? role,
    CommonStatus? status,
    int? limit,
  });

  /// Delete user (admin only)
  Future<Either<Failure, void>> deleteUser(String userId);

  /// Verify email
  Future<Either<Failure, void>> verifyEmail();

  /// Check if email is verified
  Future<Either<Failure, bool>> isEmailVerified();

  /// Refresh user data
  Future<Either<Failure, AppUser>> refreshUserData();

  /// Get user sessions
  Future<Either<Failure, List<UserSession>>> getUserSessions(String userId);

  /// Terminate session
  Future<Either<Failure, void>> terminateSession(String sessionId);

  /// Terminate all sessions except current
  Future<Either<Failure, void>> terminateAllOtherSessions();

  /// Check user permissions
  Future<Either<Failure, bool>> hasPermission(String permission);

  /// Check department access
  Future<Either<Failure, bool>> canAccessDepartment(String departmentId);

  /// Update last active timestamp
  Future<Either<Failure, void>> updateLastActive();

  /// Check if any admin user exists in the system
  Future<Either<Failure, bool>> hasAnyAdmin();

  /// Create initial admin user (ONLY FOR DEVELOPMENT/TESTING)
  /// This method should be removed or secured in production
  Future<Either<Failure, AppUser>> createInitialAdmin({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  });

  /// Get authentication state stream
  Stream<AppUser?> get authStateChanges;

  /// Get user data stream
  Stream<AppUser?> get userDataChanges;

}

// Request classes

/// Sign up request
class SignUpRequest {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? middleName;
  final String? employeeId;
  final String? phoneNumber;
  final UserRole role;
  final List<String> departmentIds;

  const SignUpRequest({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.middleName,
    this.employeeId,
    this.phoneNumber,
    this.role = UserRole.sewingOperator,
    this.departmentIds = const [],
  });
}

/// Update profile request
class UpdateProfileRequest {
  final String? firstName;
  final String? lastName;
  final String? middleName;
  final String? employeeId;
  final String? jobTitle;
  final String? department;
  final String? phoneNumber;
  final String? address;
  final DateTime? dateOfBirth;
  final String? emergencyContact;
  final String? emergencyPhone;

  const UpdateProfileRequest({
    this.firstName,
    this.lastName,
    this.middleName,
    this.employeeId,
    this.jobTitle,
    this.department,
    this.phoneNumber,
    this.address,
    this.dateOfBirth,
    this.emergencyContact,
    this.emergencyPhone,
  });
}

/// Change password request
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;
  final String? confirmPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    this.confirmPassword,
  });
}

/// User filter criteria
class UserFilterCriteria {
  final UserRole? role;
  final CommonStatus? status;
  final String? departmentId;
  final bool? emailVerified;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? lastActiveAfter;
  final DateTime? lastActiveBefore;

  const UserFilterCriteria({
    this.role,
    this.status,
    this.departmentId,
    this.emailVerified,
    this.createdAfter,
    this.createdBefore,
    this.lastActiveAfter,
    this.lastActiveBefore,
  });
}

// Response classes

/// Authentication response
class AuthResponse {
  final AppUser user;
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;

  const AuthResponse({
    required this.user,
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
  });
}

/// User list response
class UserListResponse {
  final List<AppUser> users;
  final int total;
  final bool hasMore;

  const UserListResponse({
    required this.users,
    required this.total,
    required this.hasMore,
  });
}

/// Permission check response
class PermissionResponse {
  final bool hasPermission;
  final String? reason;

  const PermissionResponse({
    required this.hasPermission,
    this.reason,
  });
}

// Exceptions

/// Authentication exception
class AuthException implements Exception {
  final String message;
  final String? code;

  const AuthException(this.message, {this.code});

  @override
  String toString() =>
      'AuthException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Permission exception
class PermissionException implements Exception {
  final String message;
  final String permission;

  const PermissionException(this.message, this.permission);

  @override
  String toString() =>
      'PermissionException: $message (Permission: $permission)';
}

/// User not found exception
class UserNotFoundException implements Exception {
  final String message;
  final String userId;

  const UserNotFoundException(this.message, this.userId);

  @override
  String toString() => 'UserNotFoundException: $message (User ID: $userId)';
}

/// Email not verified exception
class EmailNotVerifiedException implements Exception {
  final String message;

  const EmailNotVerifiedException(this.message);

  @override
  String toString() => 'EmailNotVerifiedException: $message';
}

/// Account suspended exception
class AccountSuspendedException implements Exception {
  final String message;
  final DateTime? suspendedUntil;

  const AccountSuspendedException(this.message, {this.suspendedUntil});

  @override
  String toString() =>
      'AccountSuspendedException: $message${suspendedUntil != null ? ' (Until: $suspendedUntil)' : ''}';
}

/// Session expired exception
class SessionExpiredException implements Exception {
  final String message;

  const SessionExpiredException(this.message);

  @override
  String toString() => 'SessionExpiredException: $message';
}

/// Invalid credentials exception
class InvalidCredentialsException implements Exception {
  final String message;

  const InvalidCredentialsException(this.message);

  @override
  String toString() => 'InvalidCredentialsException: $message';
}

/// Weak password exception
class WeakPasswordException implements Exception {
  final String message;
  final List<String> requirements;

  const WeakPasswordException(this.message, this.requirements);

  @override
  String toString() =>
      'WeakPasswordException: $message (Requirements: ${requirements.join(', ')})';
}

/// Email already in use exception
class EmailAlreadyInUseException implements Exception {
  final String message;
  final String email;

  const EmailAlreadyInUseException(this.message, this.email);

  @override
  String toString() => 'EmailAlreadyInUseException: $message (Email: $email)';
}
