import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final dynamic details;
  
  const Failure(
    this.message, {
    this.code,
    this.details,
  });
  
  @override
  List<Object?> get props => [message, code, details];
  
  @override
  String toString() => 'Failure(message: $message, code: $code)';
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Authorization-related failures
class AuthorizationFailure extends Failure {
  const AuthorizationFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Validation-related failures
class ValidationFailure extends Failure {
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationFailure(
    String message, {
    String? code,
    this.fieldErrors,
    dynamic details,
  }) : super(message, code: code, details: details);
  
  @override
  List<Object?> get props => [super.message, code, details, fieldErrors];
}

/// File operation failures
class FileFailure extends Failure {
  const FileFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Permission-related failures
class PermissionFailure extends Failure {
  const PermissionFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Database-related failures
class DatabaseFailure extends Failure {
  const DatabaseFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Timeout-related failures
class TimeoutFailure extends Failure {
  const TimeoutFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Format/Parsing failures
class FormatFailure extends Failure {
  const FormatFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Business logic failures
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// External service failures
class ExternalServiceFailure extends Failure {
  const ExternalServiceFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Configuration failures
class ConfigurationFailure extends Failure {
  const ConfigurationFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Unknown/Unexpected failures
class UnknownFailure extends Failure {
  const UnknownFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Specific manufacturing domain failures

/// Order-related failures
class OrderFailure extends Failure {
  const OrderFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Production-related failures
class ProductionFailure extends Failure {
  const ProductionFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Inventory-related failures
class InventoryFailure extends Failure {
  const InventoryFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Quality control failures
class QualityFailure extends Failure {
  const QualityFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Task management failures
class TaskFailure extends Failure {
  const TaskFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Workstation-related failures
class WorkstationFailure extends Failure {
  const WorkstationFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Department-related failures
class DepartmentFailure extends Failure {
  const DepartmentFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// User management failures
class UserManagementFailure extends Failure {
  const UserManagementFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Reporting failures
class ReportingFailure extends Failure {
  const ReportingFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Notification failures
class NotificationFailure extends Failure {
  const NotificationFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Unimplemented feature failure
class UnimplementedFailure extends Failure {
  const UnimplementedFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Not found failure
class NotFoundFailure extends Failure {
  const NotFoundFailure(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Operation not supported failure
class UnsupportedFailure extends Failure {
  const UnsupportedFailure(
    String message, {
    String? code = 'UNSUPPORTED_OPERATION',
    dynamic details,
  }) : super(message, code: code, details: details);
}
