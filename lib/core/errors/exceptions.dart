/// Base class for all exceptions in the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;
  
  const AppException(
    this.message, {
    this.code,
    this.details,
  });
  
  @override
  String toString() => 'AppException(message: $message, code: $code)';
}

/// Server-related exceptions
class ServerException extends AppException {
  final int? statusCode;
  
  const ServerException(
    String message, {
    String? code,
    this.statusCode,
    dynamic details,
  }) : super(message, code: code, details: details);
  
  @override
  String toString() => 'ServerException(message: $message, code: $code, statusCode: $statusCode)';
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Authorization-related exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationException(
    String message, {
    String? code,
    this.fieldErrors,
    dynamic details,
  }) : super(message, code: code, details: details);
  
  @override
  String toString() => 'ValidationException(message: $message, code: $code, fieldErrors: $fieldErrors)';
}

/// File operation exceptions
class FileException extends AppException {
  const FileException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Database-related exceptions
class DatabaseException extends AppException {
  const DatabaseException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Timeout-related exceptions
class TimeoutException extends AppException {
  const TimeoutException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Format/Parsing exceptions
class FormatException extends AppException {
  const FormatException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Business logic exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// External service exceptions
class ExternalServiceException extends AppException {
  const ExternalServiceException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Configuration exceptions
class ConfigurationException extends AppException {
  const ConfigurationException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Specific manufacturing domain exceptions

/// Order-related exceptions
class OrderException extends AppException {
  const OrderException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Production-related exceptions
class ProductionException extends AppException {
  const ProductionException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Inventory-related exceptions
class InventoryException extends AppException {
  const InventoryException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Quality control exceptions
class QualityException extends AppException {
  const QualityException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Task management exceptions
class TaskException extends AppException {
  const TaskException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Workstation-related exceptions
class WorkstationException extends AppException {
  const WorkstationException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Department-related exceptions
class DepartmentException extends AppException {
  const DepartmentException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// User management exceptions
class UserManagementException extends AppException {
  const UserManagementException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Reporting exceptions
class ReportingException extends AppException {
  const ReportingException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}

/// Notification exceptions
class NotificationException extends AppException {
  const NotificationException(
    String message, {
    String? code,
    dynamic details,
  }) : super(message, code: code, details: details);
}
