import 'dart:developer' as developer;
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/foundation.dart';

// Conditional import for web
import 'dart:html' if (dart.library.html) 'dart:html' as html;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// Web debugging helper for Chrome DevTools integration
class WebDebugHelper {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  /// Initialize web debugging features
  static void initialize() {
    if (kIsWeb && kDebugMode) {
      _setupConsoleLogging();
      _setupPerformanceMonitoring();
      _setupErrorHandling();
      _setupDevToolsIntegration();
      _addDebugInfo();
      
      _logger.i('🔧 Web debugging initialized');
      developer.log('HM Collection Debug Mode Enabled', name: 'WebDebug');
    }
  }

  /// Setup console logging for better debugging
  static void _setupConsoleLogging() {
    // Override print to use console.log
    if (kIsWeb) {
      debugPrint = (String? message, {int? wrapWidth}) {
        html.window.console.log('[Flutter] $message');
      };
    }
  }

  /// Setup performance monitoring
  static void _setupPerformanceMonitoring() {
    // Monitor frame rendering
    WidgetsBinding.instance.addTimingsCallback((List<FrameTiming> timings) {
      for (final timing in timings) {
        final buildDuration = timing.buildDuration.inMilliseconds;
        final rasterDuration = timing.rasterDuration.inMilliseconds;
        
        if (buildDuration > 16 || rasterDuration > 16) {
          _logger.w('⚠️ Slow frame detected: Build: ${buildDuration}ms, Raster: ${rasterDuration}ms');
        }
      }
    });
  }

  /// Setup global error handling
  static void _setupErrorHandling() {
    FlutterError.onError = (FlutterErrorDetails details) {
      _logger.e('Flutter Error: ${details.exception}', 
        error: details.exception, 
        stackTrace: details.stack
      );
      
      // Send to browser console
      html.window.console.error('[Flutter Error] ${details.exception}');
      html.window.console.error('[Stack Trace] ${details.stack}');
    };

    // Handle async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _logger.e('Async Error: $error', error: error, stackTrace: stack);
      html.window.console.error('[Async Error] $error');
      return true;
    };
  }

  /// Setup DevTools integration
  static void _setupDevToolsIntegration() {
    // Register service extensions for DevTools
    developer.registerExtension('ext.flutter.debugPaint', (method, parameters) async {
      return developer.ServiceExtensionResponse.result('{"enabled": true}');
    });

    developer.registerExtension('ext.flutter.inspector.show', (method, parameters) async {
      return developer.ServiceExtensionResponse.result('{"enabled": true}');
    });
  }

  /// Add debug information to window object
  static void _addDebugInfo() {
    if (kIsWeb) {
      // Add debug info to window for console access
      html.window.console.info('🚀 HM Collection Debug Info:');
      html.window.console.info('- Flutter Version: ${_getFlutterVersion()}');
      html.window.console.info('- Debug Mode: $kDebugMode');
      html.window.console.info('- Profile Mode: $kProfileMode');
      html.window.console.info('- Release Mode: $kReleaseMode');
      html.window.console.info('- Web Platform: $kIsWeb');
      html.window.console.info('- User Agent: ${html.window.navigator.userAgent}');
      html.window.console.info('- Screen Size: ${html.window.screen?.width}x${html.window.screen?.height}');
      html.window.console.info('- Viewport Size: ${html.window.innerWidth}x${html.window.innerHeight}');
    }
  }

  /// Log navigation events
  static void logNavigation(String route, {Map<String, dynamic>? parameters}) {
    _logger.i('🧭 Navigation: $route', error: parameters);
    developer.log('Navigation to: $route', name: 'Navigation');
  }

  /// Log API calls
  static void logApiCall(String method, String url, {Map<String, dynamic>? data}) {
    _logger.d('🌐 API Call: $method $url', error: data);
    developer.log('API: $method $url', name: 'API');
  }

  /// Log user interactions
  static void logUserInteraction(String action, {Map<String, dynamic>? context}) {
    _logger.d('👆 User Action: $action', error: context);
    developer.log('User: $action', name: 'UserInteraction');
  }

  /// Log performance metrics
  static void logPerformance(String metric, Duration duration, {Map<String, dynamic>? context}) {
    _logger.i('⚡ Performance: $metric took ${duration.inMilliseconds}ms', error: context);
    developer.log('Performance: $metric - ${duration.inMilliseconds}ms', name: 'Performance');
  }

  /// Log state changes
  static void logStateChange(String state, dynamic oldValue, dynamic newValue) {
    _logger.d('🔄 State Change: $state', error: {'old': oldValue, 'new': newValue});
    developer.log('State: $state changed', name: 'StateChange');
  }

  /// Get Flutter version (mock implementation)
  static String _getFlutterVersion() {
    return '3.24.0'; // This would be dynamically determined in a real app
  }

  /// Create debug overlay widget
  static Widget createDebugOverlay({required Widget child}) {
    if (!kDebugMode || !kIsWeb) return child;

    return Stack(
      children: [
        child,
        Positioned(
          top: 50,
          right: 20,
          child: _DebugPanel(),
        ),
      ],
    );
  }
}

/// Debug panel widget for web debugging
class _DebugPanel extends StatefulWidget {
  @override
  _DebugPanelState createState() => _DebugPanelState();
}

class _DebugPanelState extends State<_DebugPanel> {
  bool _isExpanded = false;
  bool _showPerformance = false;
  bool _showInspector = false;

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.bug_report, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                const Text('Debug', style: TextStyle(color: Colors.white, fontSize: 12)),
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.white,
                    size: 16,
                  ),
                  onPressed: () => setState(() => _isExpanded = !_isExpanded),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                ),
              ],
            ),
            if (_isExpanded) ...[
              const Divider(color: Colors.grey, height: 1),
              const SizedBox(height: 8),
              _buildDebugButton('Console', Icons.terminal, () => _openConsole()),
              _buildDebugButton('DevTools', Icons.developer_mode, () => _openDevTools()),
              _buildDebugButton('Inspector', Icons.search, () => _toggleInspector()),
              _buildDebugButton('Performance', Icons.speed, () => _togglePerformance()),
              _buildDebugButton('Network', Icons.network_check, () => _openNetworkTab()),
              _buildDebugButton('Storage', Icons.storage, () => _openStorageTab()),
              _buildDebugButton('Reload', Icons.refresh, () => _reloadApp()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDebugButton(String label, IconData icon, VoidCallback onPressed) {
    return SizedBox(
      width: 120,
      child: TextButton.icon(
        icon: Icon(icon, color: Colors.white, size: 14),
        label: Text(label, style: const TextStyle(color: Colors.white, fontSize: 11)),
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          minimumSize: const Size(0, 28),
        ),
      ),
    );
  }

  void _openConsole() {
    html.window.console.info('🔧 Debug Console Opened');
    // Focus on console (browser-specific)
  }

  void _openDevTools() {
    html.window.console.info('🛠️ Open Chrome DevTools (F12) for advanced debugging');
  }

  void _toggleInspector() {
    setState(() => _showInspector = !_showInspector);
    html.window.console.info('🔍 Widget Inspector: ${_showInspector ? "ON" : "OFF"}');
  }

  void _togglePerformance() {
    setState(() => _showPerformance = !_showPerformance);
    html.window.console.info('⚡ Performance Monitor: ${_showPerformance ? "ON" : "OFF"}');
  }

  void _openNetworkTab() {
    html.window.console.info('🌐 Check Network tab in DevTools for API calls');
  }

  void _openStorageTab() {
    html.window.console.info('💾 Check Application tab in DevTools for storage');
  }

  void _reloadApp() {
    html.window.location.reload();
  }
}

/// Debug-specific extensions
extension DebugExtensions on Widget {
  /// Add debug border to any widget
  Widget debugBorder([Color color = Colors.red]) {
    if (!kDebugMode) return this;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: color, width: 1),
      ),
      child: this,
    );
  }

  /// Add debug info overlay
  Widget debugInfo(String info) {
    if (!kDebugMode) return this;
    return Stack(
      children: [
        this,
        Positioned(
          top: 0,
          left: 0,
          child: Container(
            padding: const EdgeInsets.all(2),
            color: Colors.black54,
            child: Text(
              info,
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
          ),
        ),
      ],
    );
  }
}
