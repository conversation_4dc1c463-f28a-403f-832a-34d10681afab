import 'package:flutter/material.dart';

/// Application color palette
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2563EB); // Blue
  static const Color primaryLight = Color(0xFF3B82F6);
  static const Color primaryDark = Color(0xFF1D4ED8);
  static const Color primaryVariant = Color(0xFF1E40AF);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF059669); // Green
  static const Color secondaryLight = Color(0xFF10B981);
  static const Color secondaryDark = Color(0xFF047857);
  static const Color secondaryVariant = Color(0xFF065F46);
  
  // Accent Colors
  static const Color accent = Color(0xFFF59E0B); // Amber
  static const Color accentLight = Color(0xFFFBBF24);
  static const Color accentDark = Color(0xFFD97706);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Colors.transparent;
  
  // Gray Scale
  static const Color gray50 = Color(0xFFF9FAFB);
  static const Color gray100 = Color(0xFFF3F4F6);
  static const Color gray200 = Color(0xFFE5E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);
  
  // Status Colors
  static const Color success = Color(0xFF10B981); // Green
  static const Color successLight = Color(0xFF34D399);
  static const Color successDark = Color(0xFF059669);
  
  static const Color warning = Color(0xFFF59E0B); // Amber
  static const Color warningLight = Color(0xFFFBBF24);
  static const Color warningDark = Color(0xFFD97706);
  
  static const Color error = Color(0xFFEF4444); // Red
  static const Color errorLight = Color(0xFFF87171);
  static const Color errorDark = Color(0xFFDC2626);
  
  static const Color info = Color(0xFF3B82F6); // Blue
  static const Color infoLight = Color(0xFF60A5FA);
  static const Color infoDark = Color(0xFF2563EB);
  
  // Department Colors
  static const Color cutting = Color(0xFF8B5CF6); // Purple
  static const Color sewing = Color(0xFF06B6D4); // Cyan
  static const Color quality = Color(0xFFEF4444); // Red
  static const Color finishing = Color(0xFF10B981); // Green
  static const Color warehouse = Color(0xFFF59E0B); // Amber
  static const Color merchandising = Color(0xFF3B82F6); // Blue
  
  // Order Status Colors
  static const Color orderPending = Color(0xFFF59E0B); // Amber
  static const Color orderInProgress = Color(0xFF3B82F6); // Blue
  static const Color orderCompleted = Color(0xFF10B981); // Green
  static const Color orderCancelled = Color(0xFFEF4444); // Red
  static const Color orderOnHold = Color(0xFF6B7280); // Gray
  
  // Production Status Colors
  static const Color productionNotStarted = Color(0xFF6B7280); // Gray
  static const Color productionInProgress = Color(0xFF3B82F6); // Blue
  static const Color productionCompleted = Color(0xFF10B981); // Green
  static const Color productionDelayed = Color(0xFFEF4444); // Red
  static const Color productionOnHold = Color(0xFFF59E0B); // Amber
  
  // Quality Status Colors
  static const Color qualityPassed = Color(0xFF10B981); // Green
  static const Color qualityFailed = Color(0xFFEF4444); // Red
  static const Color qualityPending = Color(0xFFF59E0B); // Amber
  static const Color qualityInReview = Color(0xFF3B82F6); // Blue
  
  // Task Priority Colors
  static const Color priorityLow = Color(0xFF10B981); // Green
  static const Color priorityMedium = Color(0xFFF59E0B); // Amber
  static const Color priorityHigh = Color(0xFFEF4444); // Red
  static const Color priorityCritical = Color(0xFF7C2D12); // Dark Red
  
  // Background Colors
  static const Color background = Color(0xFFF9FAFB);
  static const Color backgroundDark = Color(0xFF111827);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1F2937);
  
  // Card Colors
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardBackgroundDark = Color(0xFF374151);
  static const Color cardBorder = Color(0xFFE5E7EB);
  static const Color cardBorderDark = Color(0xFF4B5563);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF111827);
  static const Color textPrimaryDark = Color(0xFFF9FAFB);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textSecondaryDark = Color(0xFF9CA3AF);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textTertiaryDark = Color(0xFF6B7280);
  static const Color textDisabled = Color(0xFFD1D5DB);
  static const Color textDisabledDark = Color(0xFF4B5563);
  
  // Border Colors
  static const Color border = Color(0xFFE5E7EB);
  static const Color borderDark = Color(0xFF4B5563);
  static const Color borderFocus = Color(0xFF3B82F6);
  static const Color borderError = Color(0xFFEF4444);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowDark = Color(0x40000000);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE5E7EB);
  static const Color shimmerHighlight = Color(0xFFF3F4F6);
  static const Color shimmerBaseDark = Color(0xFF374151);
  static const Color shimmerHighlightDark = Color(0xFF4B5563);
  
  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFF3B82F6), // Blue
    Color(0xFF10B981), // Green
    Color(0xFFF59E0B), // Amber
    Color(0xFFEF4444), // Red
    Color(0xFF8B5CF6), // Purple
    Color(0xFF06B6D4), // Cyan
    Color(0xFFEC4899), // Pink
    Color(0xFF84CC16), // Lime
    Color(0xFFF97316), // Orange
    Color(0xFF6366F1), // Indigo
  ];
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, warningDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Helper methods
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return orderPending;
      case 'in_progress':
      case 'in-progress':
        return orderInProgress;
      case 'completed':
        return orderCompleted;
      case 'cancelled':
        return orderCancelled;
      case 'on_hold':
      case 'on-hold':
        return orderOnHold;
      default:
        return gray500;
    }
  }
  
  static Color getDepartmentColor(String department) {
    switch (department.toLowerCase()) {
      case 'cutting':
        return cutting;
      case 'sewing':
        return sewing;
      case 'quality':
        return quality;
      case 'finishing':
        return finishing;
      case 'warehouse':
        return warehouse;
      case 'merchandising':
        return merchandising;
      default:
        return gray500;
    }
  }
  
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return priorityLow;
      case 'medium':
        return priorityMedium;
      case 'high':
        return priorityHigh;
      case 'critical':
        return priorityCritical;
      default:
        return gray500;
    }
  }
}
