import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../constants/app_constants.dart';
import 'app_colors.dart';
import 'app_text_styles.dart';

/// Application theme configuration
class AppTheme {
  /// Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: _lightColorScheme,
      textTheme: _textTheme,
      appBarTheme: _lightAppBarTheme,
      elevatedButtonTheme: _elevatedButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      textButtonTheme: _textButtonTheme,
      inputDecorationTheme: _inputDecorationTheme,
      cardTheme: _cardTheme,
      bottomNavigationBarTheme: _bottomNavigationBarThemeData,
      navigationBarTheme: _navigationBarTheme,
      floatingActionButtonTheme: _floatingActionButtonTheme,
      chipTheme: _chipTheme,
      dividerTheme: _dividerTheme,
      dialogTheme: _dialogTheme,
      scaffoldBackgroundColor: AppColors.background,
      fontFamily: AppTextStyles.primaryFont,
    );
  }

  /// Dark theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: _darkColorScheme,
      textTheme: _darkTextTheme,
      appBarTheme: _darkAppBarTheme,
      elevatedButtonTheme: _elevatedButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      textButtonTheme: _textButtonTheme,
      inputDecorationTheme: _darkInputDecorationTheme,
      cardTheme: _darkCardTheme,
      bottomNavigationBarTheme: _darkBottomNavigationBarThemeData,
      navigationBarTheme: _darkNavigationBarTheme,
      floatingActionButtonTheme: _floatingActionButtonTheme,
      chipTheme: _darkChipTheme,
      dividerTheme: _darkDividerTheme,
      dialogTheme: _darkDialogTheme,
      scaffoldBackgroundColor: AppColors.backgroundDark,
      fontFamily: AppTextStyles.primaryFont,
    );
  }

  /// Light color scheme
  static const ColorScheme _lightColorScheme = ColorScheme.light(
    primary: AppColors.primary,
    onPrimary: AppColors.white,
    primaryContainer: AppColors.primaryLight,
    onPrimaryContainer: AppColors.white,
    secondary: AppColors.secondary,
    onSecondary: AppColors.white,
    secondaryContainer: AppColors.secondaryLight,
    onSecondaryContainer: AppColors.white,
    tertiary: AppColors.accent,
    onTertiary: AppColors.white,
    error: AppColors.error,
    onError: AppColors.white,
    errorContainer: AppColors.errorLight,
    onErrorContainer: AppColors.white,
    background: AppColors.background,
    onBackground: AppColors.textPrimary,
    surface: AppColors.surface,
    onSurface: AppColors.textPrimary,
    surfaceVariant: AppColors.gray100,
    onSurfaceVariant: AppColors.textSecondary,
    outline: AppColors.border,
    outlineVariant: AppColors.gray200,
    shadow: AppColors.shadow,
    scrim: AppColors.overlay,
    inverseSurface: AppColors.gray800,
    onInverseSurface: AppColors.white,
    inversePrimary: AppColors.primaryLight,
  );

  /// Dark color scheme
  static const ColorScheme _darkColorScheme = ColorScheme.dark(
    primary: AppColors.primaryLight,
    onPrimary: AppColors.black,
    primaryContainer: AppColors.primaryDark,
    onPrimaryContainer: AppColors.white,
    secondary: AppColors.secondaryLight,
    onSecondary: AppColors.black,
    secondaryContainer: AppColors.secondaryDark,
    onSecondaryContainer: AppColors.white,
    tertiary: AppColors.accentLight,
    onTertiary: AppColors.black,
    error: AppColors.errorLight,
    onError: AppColors.black,
    errorContainer: AppColors.errorDark,
    onErrorContainer: AppColors.white,
    background: AppColors.backgroundDark,
    onBackground: AppColors.textPrimaryDark,
    surface: AppColors.surfaceDark,
    onSurface: AppColors.textPrimaryDark,
    surfaceVariant: AppColors.gray800,
    onSurfaceVariant: AppColors.textSecondaryDark,
    outline: AppColors.borderDark,
    outlineVariant: AppColors.gray700,
    shadow: AppColors.shadowDark,
    scrim: AppColors.overlay,
    inverseSurface: AppColors.gray100,
    onInverseSurface: AppColors.black,
    inversePrimary: AppColors.primaryDark,
  );

  /// Text theme
  static TextTheme get _textTheme {
    return TextTheme(
      displayLarge: AppTextStyles.displayLarge,
      displayMedium: AppTextStyles.displayMedium,
      displaySmall: AppTextStyles.displaySmall,
      headlineLarge: AppTextStyles.headlineLarge,
      headlineMedium: AppTextStyles.headlineMedium,
      headlineSmall: AppTextStyles.headlineSmall,
      titleLarge: AppTextStyles.titleLarge,
      titleMedium: AppTextStyles.titleMedium,
      titleSmall: AppTextStyles.titleSmall,
      labelLarge: AppTextStyles.labelLarge,
      labelMedium: AppTextStyles.labelMedium,
      labelSmall: AppTextStyles.labelSmall,
      bodyLarge: AppTextStyles.bodyLarge,
      bodyMedium: AppTextStyles.bodyMedium,
      bodySmall: AppTextStyles.bodySmall,
    );
  }

  /// Dark text theme
  static TextTheme get _darkTextTheme {
    return TextTheme(
      displayLarge: AppTextStyles.forDarkTheme(AppTextStyles.displayLarge),
      displayMedium: AppTextStyles.forDarkTheme(AppTextStyles.displayMedium),
      displaySmall: AppTextStyles.forDarkTheme(AppTextStyles.displaySmall),
      headlineLarge: AppTextStyles.forDarkTheme(AppTextStyles.headlineLarge),
      headlineMedium: AppTextStyles.forDarkTheme(AppTextStyles.headlineMedium),
      headlineSmall: AppTextStyles.forDarkTheme(AppTextStyles.headlineSmall),
      titleLarge: AppTextStyles.forDarkTheme(AppTextStyles.titleLarge),
      titleMedium: AppTextStyles.forDarkTheme(AppTextStyles.titleMedium),
      titleSmall: AppTextStyles.forDarkTheme(AppTextStyles.titleSmall),
      labelLarge: AppTextStyles.forDarkTheme(AppTextStyles.labelLarge),
      labelMedium: AppTextStyles.forDarkTheme(AppTextStyles.labelMedium),
      labelSmall: AppTextStyles.forDarkTheme(AppTextStyles.labelSmall),
      bodyLarge: AppTextStyles.forDarkTheme(AppTextStyles.bodyLarge),
      bodyMedium: AppTextStyles.forDarkTheme(AppTextStyles.bodyMedium),
      bodySmall: AppTextStyles.forDarkTheme(AppTextStyles.bodySmall),
    );
  }

  /// Light app bar theme
  static const AppBarTheme _lightAppBarTheme = AppBarTheme(
    backgroundColor: AppColors.surface,
    foregroundColor: AppColors.textPrimary,
    elevation: 0,
    scrolledUnderElevation: 1,
    centerTitle: true,
    titleSpacing: AppConstants.defaultPadding,
    systemOverlayStyle: SystemUiOverlayStyle.dark,
  );

  /// Dark app bar theme
  static const AppBarTheme _darkAppBarTheme = AppBarTheme(
    backgroundColor: AppColors.surfaceDark,
    foregroundColor: AppColors.textPrimaryDark,
    elevation: 0,
    scrolledUnderElevation: 1,
    centerTitle: true,
    titleSpacing: AppConstants.defaultPadding,
    systemOverlayStyle: SystemUiOverlayStyle.light,
  );

  /// Elevated button theme
  static ElevatedButtonThemeData get _elevatedButtonTheme {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        textStyle: AppTextStyles.buttonMedium,
        elevation: AppConstants.cardElevation,
      ),
    );
  }

  /// Outlined button theme
  static OutlinedButtonThemeData get _outlinedButtonTheme {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        textStyle: AppTextStyles.buttonMedium,
        side: const BorderSide(color: AppColors.border),
      ),
    );
  }

  /// Text button theme
  static TextButtonThemeData get _textButtonTheme {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        minimumSize: const Size(0, AppConstants.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        textStyle: AppTextStyles.buttonMedium,
      ),
    );
  }

  /// Input decoration theme
  static InputDecorationTheme get _inputDecorationTheme {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.border),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.border),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderFocus, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderError),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderError, width: 2),
      ),
      labelStyle: AppTextStyles.inputLabel,
      hintStyle: AppTextStyles.inputHint,
      errorStyle: AppTextStyles.inputError,
      contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
    );
  }

  /// Dark input decoration theme
  static InputDecorationTheme get _darkInputDecorationTheme {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.surfaceDark,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderDark),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderDark),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderFocus, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderError),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        borderSide: const BorderSide(color: AppColors.borderError, width: 2),
      ),
      labelStyle: AppTextStyles.forDarkTheme(AppTextStyles.inputLabel),
      hintStyle: AppTextStyles.forDarkTheme(AppTextStyles.inputHint),
      errorStyle: AppTextStyles.inputError,
      contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
    );
  }

  /// Card theme
  static CardThemeData get _cardTheme {
    return CardThemeData(
      color: AppColors.cardBackground,
      elevation: AppConstants.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        side: const BorderSide(color: AppColors.cardBorder),
      ),
      margin: const EdgeInsets.all(AppConstants.smallPadding),
    );
  }

  /// Dark card theme
  static CardThemeData get _darkCardTheme {
    return CardThemeData(
      color: AppColors.cardBackgroundDark,
      elevation: AppConstants.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        side: const BorderSide(color: AppColors.cardBorderDark),
      ),
      margin: const EdgeInsets.all(AppConstants.smallPadding),
    );
  }

  /// Bottom navigation bar theme
  static const BottomNavigationBarThemeData _bottomNavigationBarThemeData = BottomNavigationBarThemeData(
    backgroundColor: AppColors.surface,
    selectedItemColor: AppColors.primary,
    unselectedItemColor: AppColors.textSecondary,
    type: BottomNavigationBarType.fixed,
    elevation: AppConstants.cardElevation,
  );

  /// Dark bottom navigation bar theme
  static const BottomNavigationBarThemeData _darkBottomNavigationBarThemeData = BottomNavigationBarThemeData(
    backgroundColor: AppColors.surfaceDark,
    selectedItemColor: AppColors.primaryLight,
    unselectedItemColor: AppColors.textSecondaryDark,
    type: BottomNavigationBarType.fixed,
    elevation: AppConstants.cardElevation,
  );

  /// Navigation bar theme
  static NavigationBarThemeData get _navigationBarTheme {
    return NavigationBarThemeData(
      backgroundColor: AppColors.surface,
      indicatorColor: AppColors.primaryLight,
      labelTextStyle: WidgetStateProperty.all(AppTextStyles.navLabel),
      elevation: AppConstants.cardElevation,
    );
  }

  /// Dark navigation bar theme
  static NavigationBarThemeData get _darkNavigationBarTheme {
    return NavigationBarThemeData(
      backgroundColor: AppColors.surfaceDark,
      indicatorColor: AppColors.primaryDark,
      labelTextStyle: WidgetStateProperty.all(
        AppTextStyles.forDarkTheme(AppTextStyles.navLabel),
      ),
      elevation: AppConstants.cardElevation,
    );
  }

  /// Floating action button theme
  static const FloatingActionButtonThemeData _floatingActionButtonTheme = FloatingActionButtonThemeData(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.white,
    elevation: AppConstants.cardElevation,
  );

  /// Chip theme
  static ChipThemeData get _chipTheme {
    return ChipThemeData(
      backgroundColor: AppColors.gray100,
      selectedColor: AppColors.primaryLight,
      labelStyle: AppTextStyles.labelSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
    );
  }

  /// Dark chip theme
  static ChipThemeData get _darkChipTheme {
    return ChipThemeData(
      backgroundColor: AppColors.gray800,
      selectedColor: AppColors.primaryDark,
      labelStyle: AppTextStyles.forDarkTheme(AppTextStyles.labelSmall),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
    );
  }

  /// Divider theme
  static const DividerThemeData _dividerTheme = DividerThemeData(
    color: AppColors.border,
    thickness: 1,
    space: 1,
  );

  /// Dark divider theme
  static const DividerThemeData _darkDividerTheme = DividerThemeData(
    color: AppColors.borderDark,
    thickness: 1,
    space: 1,
  );

  /// Dialog theme
  static DialogThemeData get _dialogTheme {
    return DialogThemeData(
      backgroundColor: AppColors.surface,
      elevation: AppConstants.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      titleTextStyle: AppTextStyles.titleLarge,
      contentTextStyle: AppTextStyles.bodyMedium,
    );
  }

  /// Dark dialog theme
  static DialogThemeData get _darkDialogTheme {
    return DialogThemeData(
      backgroundColor: AppColors.surfaceDark,
      elevation: AppConstants.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      titleTextStyle: AppTextStyles.forDarkTheme(AppTextStyles.titleLarge),
      contentTextStyle: AppTextStyles.forDarkTheme(AppTextStyles.bodyMedium),
    );
  }
}
