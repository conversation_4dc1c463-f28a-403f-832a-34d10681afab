import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';

import 'core/auth/providers/auth_provider.dart';
import 'core/auth/widgets/auth_splash_screen.dart';
import 'core/constants/app_constants.dart';
import 'core/injection/injection_container.dart' as di;
import 'core/theme/app_theme.dart';
import 'features/auth/presentation/bloc/firebase_auth_bloc.dart';
import 'features/manufacturing/presentation/bloc/employee_bloc.dart';
import 'firebase_options.dart';

void main() async {
  // Ensure that the Flutter engine is initialized before running any other code.
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred screen orientations for mobile devices.
  if (!kIsWeb) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  // Asynchronously initialize Firebase and dependency injection.
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await di.configureDependencies();

  // Run the application only after all initialization is complete.
  runApp(const HMCollectionApp());
}

class HMCollectionApp extends StatelessWidget {
  const HMCollectionApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Use MultiProvider to make multiple BLoCs and Providers available
    // to the widget tree.
    return MultiProvider(
      providers: [
        // The AuthProvider is needed by the AuthSplashScreen.
        ChangeNotifierProvider<AuthProvider>(
          create: (_) => di.getIt<AuthProvider>(),
        ),
        // The FirebaseAuthBloc handles user authentication state.
        BlocProvider<FirebaseAuthBloc>(
          create: (context) => di.getIt<FirebaseAuthBloc>()..add(const FirebaseAuthStarted()),
        ),
        // The EmployeeBloc is used for managing employee data.
        BlocProvider<EmployeeBloc>(
          create: (context) => di.getIt<EmployeeBloc>(),
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        debugShowCheckedModeBanner: false,
        home: const AuthSplashScreen(),
      ),
    );
  }
}
